﻿@{
    ViewData["Title"] = "Privacy Policy";
}


<style>
    .card {
        margin-bottom: 20px;
        border: none;
        transition: transform 0.3s ease-in-out, box-shadow 0.3s ease-in-out;
        box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
    }

        .card:hover {
            transform: translateY(-5px);
            box-shadow: 0 4px 8px rgba(0, 0, 0, 0.15);
        }

    .card-header {
        background-color: #F8F9FA;
        border-bottom: 1px solid #E9ECEE;
        padding: 10px 15px;
    }

    .card-title {
        font-weight: bold;
        margin-bottom: 0;
    }

    .company-title {
        font-weight: 600;
        color: #007bff;
    }

    .card-body {
        padding: 15px;
        background-color: #ffffff;
    }

    .card-text {
        font-size: 14px;
        margin-bottom: 10px;
    }

        .card-text i {
            margin-right: 5px;
            color: #6c757d;
        }

    .card-link {
        background-color: #007bff;
        color: #fff;
        border: none;
        transition: background-color 0.3s ease-in-out;
    }

        .card-link:hover {
            background-color: darken(#007bff, 10%);
        }

    .morecontent span {
        display: none;
    }

    .morelink {
        display: block;
        color: #007bff;
        cursor: pointer;
    }
</style>


<div class="container">
    <div class="row">
        <!-- Card Block -->
        <!-- Repeat this block for each card -->
        <div class="col-md-4">
            <div class="card animated-card mt-4">
                <div class="card-header">
                    <h5 class="card-title text-center">Software Engineer</h5>
                </div>
                <div class="card-body">
                    <h6 class="company-title">Acme Corporation</h6>
                    <p class="card-text"><i class="fas fa-map-marker-alt"></i> San Francisco, CA</p>
                    <p class="card-text"><i class="fas fa-briefcase"></i> Full-time</p>
                    <p class="card-text"><i class="fas fa-calendar-alt"></i> Start Date: 2023-12-01</p>
                    <p class="card-text description"> lorem Long text more than 250 characterLong text more than 250 characterLong text more than 250 characterLong text more than 250 characterLong
                    <a href="#" class="card-link btn btn-primary">Apply Now</a>
                </div>
            </div>
        </div>
        <!-- Additional Dummy Cards with similar structure -->
        <!-- ... -->
    </div>
</div>

<script>
    $(document).ready(function () {
        console.log("Document is ready");

        var showChar = 250;
        var ellipsestext = "...";
        var moretext = "Read More";
        var lesstext = "Read Less";

        $('.description').each(function () {
            var content = $(this).html();

            if (content.length > showChar) {
                var shownText = content.substr(0, showChar);
                var hiddenText = content.substr(showChar, content.length - showChar);
                var html = shownText + '<span class="moreellipses">' + ellipsestext + '&nbsp;</span><span class="morecontent"><span>' + hiddenText + '</span>&nbsp;&nbsp;<a href="#" class="morelink">' + moretext + '</a></span>';
                $(this).html(html);
            }
        });

        $(".morelink").click(function () {
            if ($(this).hasClass("less")) {
                $(this).removeClass("less");
                $(this).html(moretext);
            } else {
                $(this).addClass("less");
                $(this).html(lesstext);
            }
            $(this).parent().prev().toggle();
            $(this).prev().toggle();
            return false;
        });

        console.log("jQuery script executed");
    });
</script>

