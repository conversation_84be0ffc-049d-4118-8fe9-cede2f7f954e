﻿@model OmanInternship.Models.Location

@{
    ViewData["Title"] = "Delete";
    Layout = "~/Views/Shared/_Layout.cshtml";

}

<h1>Delete</h1>

<h3>Are you sure you want to delete this?</h3>
<div>
    <h4>Location</h4>
    <hr />
    <dl class="row">
        <dt class="col-sm-2">
            @Html.DisplayNameFor(model => model.LocationName)
        </dt>
        <dd class="col-sm-10">
            @Html.DisplayFor(model => model.LocationName)
        </dd>
    </dl>

    <form asp-action="Delete">
        <input type="hidden" asp-for="id" />
        <input type="submit" value="Delete" class="btn btn-danger" /> |
        <a asp-action="Index">Back to List</a>
    </form>
</div>
