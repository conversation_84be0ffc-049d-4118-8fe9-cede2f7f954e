﻿@model OmanInternship.Models.City

@{
    ViewData["Title"] = "Edit";
    Layout = "~/Views/Shared/_Layout.cshtml";
}

<h1>Edit</h1>

<h4>City</h4>
<hr />
<div class="row">
    <div class="col-md-4">
        <form asp-action="Edit">
            <div asp-validation-summary="ModelOnly" class="text-danger"></div>
            <input type="hidden" asp-for="CityId" />
            <div class="form-group">
                <label asp-for="CityName" class="control-label"></label>
                <input asp-for="CityName" class="form-control" />
                <span asp-validation-for="CityName" class="text-danger"></span>
            </div>
            <div class="form-group">
                <label asp-for="LocationId" class="control-label"></label>
                <select asp-for="LocationId" class="form-control" asp-items="ViewBag.LocationId"></select>
                <span asp-validation-for="LocationId" class="text-danger"></span>
            </div>
            <div class="form-group">
                <input type="submit" value="Save" class="btn btn-primary" />
            </div>
        </form>
    </div>
</div>

<div>
    <a asp-action="Index">Back to List</a>
</div>

