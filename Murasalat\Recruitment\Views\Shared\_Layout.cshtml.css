﻿/* Please see documentation at https://learn.microsoft.com/aspnet/core/client-side/bundling-and-minification
for details on configuring this project to bundle and minify static web assets. */
body {
  font-family: 'Cairo', sans-serif;
  overflow-x: hidden;
  background-color: #f8f9fa;
}
.sidebar {
  height: 100vh;
  position: fixed;
  top: 0;
  right: 0;
  width: 250px;
  background-color: #ffffff;  /* Light background */
  padding-top: 0;
  transition: all 0.3s ease;
  z-index: 1000;
  box-shadow: 0 0 15px rgba(0,0,0,0.05);
  overflow: hidden;
}
.sidebar.collapsed {
  width: 70px;
}
.main-content {
  margin-right: 250px;
  transition: all 0.3s ease;
  padding: 20px;
}
.main-content.expanded {
  margin-right: 70px;
}
.nav-link {
  padding: 12px 20px;
  color: #333333;  /* Dark gray, almost black */
  display: flex;
  align-items: center;
  white-space: nowrap;
  border-radius: 5px;
  margin: 5px 10px;
  position: relative;
}
.nav-link:hover {
  background-color: #f8f9fa;  /* Light gray on hover */
  color: #000000;  /* Black text on hover */
}
.nav-link.active {
  background-color: #e9ecef;  /* Slightly darker background for active */
  color: #000000;  /* Black text for active state */
}
.nav-link i {
  width: 24px;
  text-align: center;
  margin-left: 10px;
  font-size: 1.1rem;
  color: #1b6ec2;  /* Gray icons */
}
.brand-container {
  padding: 20px;
  text-align: right;
  border-bottom: 1px solid #e9ecef;
  margin-bottom: 20px;
  color: #333333;  /* Dark text for header */
  background-color: #ffffff;  /* White background */
  display: flex;
  align-items: center;
  justify-content: space-between;
}
.toggle-btn {
  background: transparent;
  color: #666666;
  border: none;
  padding: 8px;
  cursor: pointer;
  margin-left: 10px;
  transition: all 0.3s ease;
}
.toggle-btn:hover {
  color: #000000;
}
.submenu {
  padding-right: 35px;
  background-color: #f8f9fa;  /* Light gray background */
  border-radius: 5px;
  margin: 0 10px;
}
.submenu .nav-link {
  color: #333333;  /* Dark gray text */
  margin: 2px 0;
}
.nav-link[aria-expanded="true"] {
  color: #000000;  /* Black text for expanded menu */
  background-color: #f8f9fa;
}
.nav-link[aria-expanded="true"] i.fa-chevron-down {
  transform: rotate(180deg);
  transition: transform 0.3s ease;
}
.menu-text {
  transition: opacity 0.3s;
  font-size: 0.9rem;
}
.collapsed .menu-text {
  opacity: 0;
  display: none;
}
.collapsed .submenu {
  padding-right: 0;
  margin: 0;
}
.rotate-180 {
  transform: rotate(180deg);
  transition: transform 0.3s ease;
}










