﻿@model AspCoreMVC6.Models.Publisher

@{
    ViewData["Title"] = "Details";
}

<h1>Details</h1>

<div>
    <h4>Publisher</h4>
    <hr />
    <dl class="row">
        <dt class = "col-sm-2">
            @Html.DisplayNameFor(model => model.PublisherName)
        </dt>
        <dd class = "col-sm-10">
            @Html.DisplayFor(model => model.PublisherName)
        </dd>
    </dl>
</div>
<div>
    <a asp-action="Edit" asp-route-id="@Model.PublisherId">Edit</a> |
    <a asp-action="Index">Back to List</a>
</div>
