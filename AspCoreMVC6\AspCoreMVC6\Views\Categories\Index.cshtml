﻿@model IEnumerable<AspCoreMVC6.Models.Category>

@{
    ViewData["Title"] = "Index";
}

<h1>Index</h1>

<p>
    <a asp-action="Create">Create New</a>
</p>
<table class="table">
    <thead>
        <tr>
            <th>
                @Html.DisplayNameFor(model => model.Categoryname)
            </th>
            <th></th>
        </tr>
    </thead>
    <tbody>
@foreach (var item in Model) {
        <tr>
            <td>
                @Html.DisplayFor(modelItem => item.Categoryname)
            </td>
            <td>
                <a asp-action="Edit" asp-route-id="@item.CategoryId">Edit</a> |
                <a asp-action="Details" asp-route-id="@item.CategoryId">Details</a> |
                <a asp-action="Delete" asp-route-id="@item.CategoryId">Delete</a>
            </td>
        </tr>
}
    </tbody>
</table>
