﻿@{
    ViewData["Title"] = "الرئيسية";
}

<div class="dashboard-container">
    <div class="welcome-section mb-4">
        <h3 class="display-6 mb-3 text-primary">مرحباً بك في نظام إدارة الملفات والرسائل</h3>
        <p class="lead">نظام متكامل لإدارة المراسلات الواردة والصادرة وأرشفة الملفات</p>
    </div>

    <!-- First Row: Summary Cards -->
    <div class="row g-4">
        <div class="col-xl-2 col-md-4">
            <div class="card border-0 shadow-sm h-100">
                <div class="card-body text-center">
                    <i class="fas fa-inbox fa-2x mb-3 text-primary"></i>
                    <h5 class="card-title">الرسائل الواردة</h5>
                    <h3 class="card-text">1,248</h3>
                    <p class="small text-muted"><i class="fas fa-arrow-up text-success"></i> 12% عن الشهر الماضي</p>
                </div>
            </div>
        </div>

        <div class="col-xl-2 col-md-4">
            <div class="card border-0 shadow-sm h-100">
                <div class="card-body text-center">
                    <i class="fas fa-paper-plane fa-2x mb-3 text-success"></i>
                    <h5 class="card-title">الرسائل الصادرة</h5>
                    <h3 class="card-text">982</h3>
                    <p class="small text-muted"><i class="fas fa-arrow-down text-danger"></i> 5% عن الشهر الماضي</p>
                </div>
            </div>
        </div>

        <div class="col-xl-2 col-md-4">
            <div class="card border-0 shadow-sm h-100">
                <div class="card-body text-center">
                    <i class="fas fa-file-alt fa-2x mb-3 text-warning"></i>
                    <h5 class="card-title">الملفات المحفوظة</h5>
                    <h3 class="card-text">3,756</h3>
                    <p class="small text-muted"><i class="fas fa-arrow-up text-success"></i> 24% عن الشهر الماضي</p>
                </div>
            </div>
        </div>

        <div class="col-xl-2 col-md-4">
            <div class="card border-0 shadow-sm h-100">
                <div class="card-body text-center">
                    <i class="fas fa-clock fa-2x mb-3 text-info"></i>
                    <h5 class="card-title">بانتظار المعالجة</h5>
                    <h3 class="card-text">187</h3>
                    <p class="small text-muted"><i class="fas fa-arrow-down text-success"></i> 8% عن الشهر الماضي</p>
                </div>
            </div>
        </div>

        <div class="col-xl-2 col-md-4">
            <div class="card border-0 shadow-sm h-100">
                <div class="card-body text-center">
                    <i class="fas fa-check-circle fa-2x mb-3 text-success"></i>
                    <h5 class="card-title">مكتملة</h5>
                    <h3 class="card-text">2,843</h3>
                    <p class="small text-muted"><i class="fas fa-arrow-up text-success"></i> 15% عن الشهر الماضي</p>
                </div>
            </div>
        </div>

        <div class="col-xl-2 col-md-4">
            <div class="card border-0 shadow-sm h-100">
                <div class="card-body text-center">
                    <i class="fas fa-exclamation-triangle fa-2x mb-3 text-danger"></i>
                    <h5 class="card-title">متأخرة</h5>
                    <h3 class="card-text">42</h3>
                    <p class="small text-muted"><i class="fas fa-arrow-up text-danger"></i> 3% عن الشهر الماضي</p>
                </div>
            </div>
        </div>
    </div>

    <!-- Second Row: Main Charts -->
    <div class="row mt-4">
        <!-- Activity Timeline -->
        <div class="col-xl-8">
            <div class="card border-0 shadow-sm h-100">
                <div class="card-body">
                    <h5 class="card-title d-flex justify-content-between">
                        <span>حركة المراسلات خلال السنة</span>
                        <div class="btn-group btn-group-sm">
                            <button class="btn btn-outline-secondary active" data-period="year">سنة</button>
                            <button class="btn btn-outline-secondary" data-period="month">شهر</button>
                            <button class="btn btn-outline-secondary" data-period="week">أسبوع</button>
                        </div>
                    </h5>
                    <canvas id="activityTimelineChart"></canvas>
                </div>
            </div>
        </div>

        <!-- File Type Distribution -->
        <div class="col-xl-4">
            <div class="card border-0 shadow-sm h-100">
                <div class="card-body">
                    <h5 class="card-title">توزيع أنواع الملفات</h5>
                    <canvas id="fileTypeChart"></canvas>
                    <div class="legend-container mt-3 text-center" id="fileTypeLegend"></div>
                </div>
            </div>
        </div>
    </div>

    <!-- Third Row: Secondary Charts -->
    <div class="row mt-4">
        <!-- Department Performance -->
        <div class="col-xl-4">
            <div class="card border-0 shadow-sm h-100">
                <div class="card-body">
                    <h5 class="card-title">أداء الأقسام في المعالجة</h5>
                    <canvas id="departmentPerformanceChart"></canvas>
                </div>
            </div>
        </div>

        <!-- Processing Time -->
        <div class="col-xl-4">
            <div class="card border-0 shadow-sm h-100">
                <div class="card-body">
                    <h5 class="card-title">متوسط وقت المعالجة (بالساعات)</h5>
                    <canvas id="processingTimeChart"></canvas>
                </div>
            </div>
        </div>

        <!-- Priority Distribution -->
        <div class="col-xl-4">
            <div class="card border-0 shadow-sm h-100">
                <div class="card-body">
                    <h5 class="card-title">توزيع الأولويات</h5>
                    <canvas id="priorityRadarChart"></canvas>
                </div>
            </div>
        </div>
    </div>

    <!-- Fourth Row: Additional Visualizations -->
    <div class="row mt-4">
        <!-- Recent Activities -->
        <div class="col-xl-6">
            <div class="card border-0 shadow-sm h-100">
                <div class="card-body">
                    <h5 class="card-title">آخر النشاطات</h5>
                    <div class="activity-timeline">
                        <div class="activity-item">
                            <div class="activity-badge success"></div>
                            <div class="activity-content">
                                <div class="d-flex justify-content-between">
                                    <span class="fw-bold">تم معالجة الوثيقة #2451</span>
                                    <small class="text-muted">منذ 5 دقائق</small>
                                </div>
                                <p>تمت معالجة طلب التوثيق من قسم الموارد البشرية</p>
                            </div>
                        </div>
                        <div class="activity-item">
                            <div class="activity-badge primary"></div>
                            <div class="activity-content">
                                <div class="d-flex justify-content-between">
                                    <span class="fw-bold">رسالة جديدة واردة</span>
                                    <small class="text-muted">منذ 25 دقيقة</small>
                                </div>
                                <p>رسالة رسمية من الإدارة العامة للمشاريع</p>
                            </div>
                        </div>
                        <div class="activity-item">
                            <div class="activity-badge warning"></div>
                            <div class="activity-content">
                                <div class="d-flex justify-content-between">
                                    <span class="fw-bold">تنبيه تأخير</span>
                                    <small class="text-muted">منذ ساعة</small>
                                </div>
                                <p>وثيقة #1872 تجاوزت وقت المعالجة المحدد</p>
                            </div>
                        </div>
                        <div class="activity-item">
                            <div class="activity-badge info"></div>
                            <div class="activity-content">
                                <div class="d-flex justify-content-between">
                                    <span class="fw-bold">تحديث حالة</span>
                                    <small class="text-muted">منذ ساعتين</small>
                                </div>
                                <p>تم نقل الملف #3321 إلى مرحلة المراجعة النهائية</p>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- Top Users -->
        <div class="col-xl-6">
            <div class="card border-0 shadow-sm h-100">
                <div class="card-body">
                    <h5 class="card-title">أكثر المستخدمين نشاطاً</h5>
                    <div class="user-performance">
                        <div class="user-item">
                            <div class="user-avatar">
                                <img src="https://via.placeholder.com/40" alt="User" class="rounded-circle">
                            </div>
                            <div class="user-info">
                                <span class="fw-bold">أحمد محمد</span>
                                <small class="text-muted">إدارة الموارد البشرية</small>
                            </div>
                            <div class="user-stats">
                                <div class="progress">
                                    <div class="progress-bar bg-success" role="progressbar" style="width: 85%"></div>
                                </div>
                                <span>247 نشاط</span>
                            </div>
                        </div>
                        <div class="user-item">
                            <div class="user-avatar">
                                <img src="https://via.placeholder.com/40" alt="User" class="rounded-circle">
                            </div>
                            <div class="user-info">
                                <span class="fw-bold">سارة عبدالله</span>
                                <small class="text-muted">الإدارة المالية</small>
                            </div>
                            <div class="user-stats">
                                <div class="progress">
                                    <div class="progress-bar bg-info" role="progressbar" style="width: 72%"></div>
                                </div>
                                <span>198 نشاط</span>
                            </div>
                        </div>
                        <div class="user-item">
                            <div class="user-avatar">
                                <img src="https://via.placeholder.com/40" alt="User" class="rounded-circle">
                            </div>
                            <div class="user-info">
                                <span class="fw-bold">خالد أحمد</span>
                                <small class="text-muted">إدارة المشاريع</small>
                            </div>
                            <div class="user-stats">
                                <div class="progress">
                                    <div class="progress-bar bg-warning" role="progressbar" style="width: 65%"></div>
                                </div>
                                <span>176 نشاط</span>
                            </div>
                        </div>
                        <div class="user-item">
                            <div class="user-avatar">
                                <img src="https://via.placeholder.com/40" alt="User" class="rounded-circle">
                            </div>
                            <div class="user-info">
                                <span class="fw-bold">نورة سعيد</span>
                                <small class="text-muted">إدارة الجودة</small>
                            </div>
                            <div class="user-stats">
                                <div class="progress">
                                    <div class="progress-bar bg-danger" role="progressbar" style="width: 53%"></div>
                                </div>
                                <span>142 نشاط</span>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>


    <style>
        /* Dashboard Styles */
        .dashboard-container {
            padding: 2rem;
        }

        .welcome-section {
            text-align: right;
            padding: 2rem 0;
        }

        .card {
            transition: transform 0.2s;
            border-radius: 0.5rem;
            margin-bottom: 1.5rem;
        }

            .card:hover {
                transform: translateY(-5px);
                box-shadow: 0 0.5rem 1.5rem rgba(0, 0, 0, 0.1) !important;
            }

        /* Chart Containers */
        canvas {
            max-height: 300px;
            width: 100% !important;
        }

        /* Activity Timeline */
        .activity-timeline {
            position: relative;
            padding-right: 1.5rem;
        }

        .activity-item {
            position: relative;
            padding-bottom: 1.5rem;
            padding-right: 2rem;
        }

        .activity-badge {
            position: absolute;
            right: -0.5rem;
            top: 0;
            width: 1rem;
            height: 1rem;
            border-radius: 50%;
            border: 2px solid #fff;
        }

            .activity-badge.primary {
                background-color: #4e73df;
            }

            .activity-badge.success {
                background-color: #1cc88a;
            }

            .activity-badge.info {
                background-color: #36b9cc;
            }

            .activity-badge.warning {
                background-color: #f6c23e;
            }

            .activity-badge.danger {
                background-color: #e74a3b;
            }

        .activity-content {
            background-color: #f8f9fa;
            padding: 1rem;
            border-radius: 0.5rem;
        }

        /* User Performance */
        .user-item {
            display: flex;
            align-items: center;
            padding: 0.75rem 0;
            border-bottom: 1px solid #eee;
        }

        .user-avatar {
            margin-left: 1rem;
        }

        .user-info {
            flex: 1;
        }

        .user-stats {
            width: 50%;
        }

            .user-stats .progress {
                height: 0.5rem;
                margin-bottom: 0.25rem;
            }

            .user-stats span {
                font-size: 0.8rem;
                color: #6c757d;
            }

        /* Legend Containers */
        .legend-container {
            display: flex;
            flex-wrap: wrap;
            justify-content: center;
            gap: 1rem;
        }

        .legend-item {
            display: flex;
            align-items: center;
            margin: 0 0.5rem;
        }

        .legend-color {
            width: 1rem;
            height: 1rem;
            border-radius: 0.25rem;
            margin-left: 0.5rem;
        }
    </style>


@section Scripts {
    <script src="https://cdn.jsdelivr.net/npm/chart.js"></script>
    <script src="https://cdn.jsdelivr.net/npm/chartjs-plugin-datalabels@2.0.0"></script>
    <script>
        document.addEventListener('DOMContentLoaded', function () {
            // Activity Timeline Chart (Main Chart)
            const timelineCtx = document.getElementById('activityTimelineChart').getContext('2d');
            const activityTimelineChart = new Chart(timelineCtx, {
                type: 'bar',
                data: {
                    labels: ['يناير', 'فبراير', 'مارس', 'أبريل', 'مايو', 'يونيو', 'يوليو', 'أغسطس', 'سبتمبر', 'أكتوبر', 'نوفمبر', 'ديسمبر'],
                    datasets: [
                        {
                            label: 'واردة',
                            data: [120, 190, 150, 220, 180, 250, 210, 230, 280, 260, 300, 350],
                            backgroundColor: '#4e73df',
                            borderRadius: 4
                        },
                        {
                            label: 'صادرة',
                            data: [80, 120, 100, 150, 130, 180, 160, 170, 200, 190, 220, 250],
                            backgroundColor: '#1cc88a',
                            borderRadius: 4
                        }
                    ]
                },
                options: {
                    responsive: true,
                    maintainAspectRatio: false,
                    plugins: {
                        legend: {
                            position: 'top',
                            rtl: true
                        },
                        tooltip: {
                            mode: 'index',
                            intersect: false
                        }
                    },
                    scales: {
                        x: {
                            stacked: false,
                            grid: {
                                display: false
                            }
                        },
                        y: {
                            stacked: false,
                            beginAtZero: true,
                            ticks: {
                                stepSize: 100
                            }
                        }
                    },
                    interaction: {
                        intersect: false,
                        mode: 'nearest'
                    }
                }
            });

            // File Type Distribution Chart
            const fileTypeCtx = document.getElementById('fileTypeChart').getContext('2d');
            const fileTypeChart = new Chart(fileTypeCtx, {
                type: 'doughnut',
                data: {
                    labels: ['مستندات', 'صور', 'PDF', 'Excel', 'Word', 'أخرى'],
                    datasets: [{
                        data: [35, 20, 15, 12, 10, 8],
                        backgroundColor: [
                            '#4e73df',
                            '#1cc88a',
                            '#36b9cc',
                            '#f6c23e',
                            '#e74a3b',
                            '#858796'
                        ],
                        borderWidth: 0
                    }]
                },
                options: {
                    responsive: true,
                    maintainAspectRatio: false,
                    plugins: {
                        legend: {
                            display: false
                        },
                        datalabels: {
                            display: false
                        }
                    },
                    cutout: '70%'
                },
                plugins: [ChartDataLabels]
            });

            // Create custom legend for file type chart
            const fileTypeLegend = document.getElementById('fileTypeLegend');
            fileTypeChart.data.labels.forEach((label, i) => {
                const legendItem = document.createElement('div');
                legendItem.className = 'legend-item';

                const colorBox = document.createElement('div');
                colorBox.className = 'legend-color';
                colorBox.style.backgroundColor = fileTypeChart.data.datasets[0].backgroundColor[i];

                const textSpan = document.createElement('span');
                textSpan.textContent = label;

                legendItem.appendChild(colorBox);
                legendItem.appendChild(textSpan);
                fileTypeLegend.appendChild(legendItem);
            });

            // Department Performance Chart
            const deptPerfCtx = document.getElementById('departmentPerformanceChart').getContext('2d');
            const departmentPerformanceChart = new Chart(deptPerfCtx, {
                type: 'horizontalBar',
                data: {
                    labels: ['الموارد البشرية', 'المالية', 'المشاريع', 'التقنية', 'الجودة'],
                    datasets: [{
                        label: 'متوسط وقت المعالجة (أيام)',
                        data: [2.5, 3.1, 4.2, 1.8, 2.9],
                        backgroundColor: [
                            'rgba(78, 115, 223, 0.8)',
                            'rgba(28, 200, 138, 0.8)',
                            'rgba(54, 185, 204, 0.8)',
                            'rgba(246, 194, 62, 0.8)',
                            'rgba(231, 74, 59, 0.8)'
                        ],
                        borderColor: [
                            'rgba(78, 115, 223, 1)',
                            'rgba(28, 200, 138, 1)',
                            'rgba(54, 185, 204, 1)',
                            'rgba(246, 194, 62, 1)',
                            'rgba(231, 74, 59, 1)'
                        ],
                        borderWidth: 1
                    }]
                },
                options: {
                    indexAxis: 'y',
                    responsive: true,
                    maintainAspectRatio: false,
                    plugins: {
                        legend: {
                            display: false
                        }
                    },
                    scales: {
                        x: {
                            beginAtZero: true
                        }
                    }
                }
            });

            // Processing Time Chart
            const processingTimeCtx = document.getElementById('processingTimeChart').getContext('2d');
            const processingTimeChart = new Chart(processingTimeCtx, {
                type: 'line',
                data: {
                    labels: ['يناير', 'فبراير', 'مارس', 'أبريل', 'مايو', 'يونيو'],
                    datasets: [{
                        label: 'متوسط وقت المعالجة',
                        data: [48, 42, 36, 30, 28, 24],
                        fill: true,
                        backgroundColor: 'rgba(78, 115, 223, 0.1)',
                        borderColor: 'rgba(78, 115, 223, 1)',
                        tension: 0.4,
                        pointBackgroundColor: '#fff',
                        pointBorderColor: '#4e73df',
                        pointRadius: 4,
                        pointHoverRadius: 6
                    }]
                },
                options: {
                    responsive: true,
                    maintainAspectRatio: false,
                    plugins: {
                        legend: {
                            display: false
                        }
                    },
                    scales: {
                        y: {
                            beginAtZero: false,
                            min: 20
                        }
                    }
                }
            });

            // Priority Radar Chart
            const priorityCtx = document.getElementById('priorityRadarChart').getContext('2d');
            const priorityRadarChart = new Chart(priorityCtx, {
                type: 'radar',
                data: {
                    labels: ['عاجل', 'مهم', 'عادي', 'منخفض', 'متابعة'],
                    datasets: [{
                        label: 'توزيع الأولويات',
                        data: [65, 59, 80, 45, 30],
                        backgroundColor: 'rgba(78, 115, 223, 0.2)',
                        borderColor: 'rgba(78, 115, 223, 1)',
                        pointBackgroundColor: 'rgba(78, 115, 223, 1)',
                        pointBorderColor: '#fff',
                        pointHoverBackgroundColor: '#fff',
                        pointHoverBorderColor: 'rgba(78, 115, 223, 1)'
                    }]
                },
                options: {
                    responsive: true,
                    maintainAspectRatio: false,
                    plugins: {
                        legend: {
                            display: false
                        }
                    },
                    scales: {
                        r: {
                            angleLines: {
                                display: true
                            },
                            suggestedMin: 0,
                            suggestedMax: 100
                        }
                    }
                }
            });

            // Period selector functionality
            document.querySelectorAll('[data-period]').forEach(btn => {
                btn.addEventListener('click', function () {
                    document.querySelectorAll('[data-period]').forEach(b => b.classList.remove('active'));
                    this.classList.add('active');

                    // Here you would typically fetch new data based on selected period
                    // For demo, we'll just update the chart with random data
                    const period = this.dataset.period;
                    let labels, incomingData, outgoingData;

                    if (period === 'year') {
                        labels = ['يناير', 'فبراير', 'مارس', 'أبريل', 'مايو', 'يونيو', 'يوليو', 'أغسطس', 'سبتمبر', 'أكتوبر', 'نوفمبر', 'ديسمبر'];
                        incomingData = [120, 190, 150, 220, 180, 250, 210, 230, 280, 260, 300, 350];
                        outgoingData = [80, 120, 100, 150, 130, 180, 160, 170, 200, 190, 220, 250];
                    } else if (period === 'month') {
                        labels = ['الأسبوع 1', 'الأسبوع 2', 'الأسبوع 3', 'الأسبوع 4'];
                        incomingData = [85, 72, 93, 105];
                        outgoingData = [60, 55, 70, 80];
                    } else { // week
                        labels = ['السبت', 'الأحد', 'الإثنين', 'الثلاثاء', 'الأربعاء', 'الخميس', 'الجمعة'];
                        incomingData = [15, 20, 25, 22, 30, 28, 18];
                        outgoingData = [10, 15, 18, 20, 25, 22, 12];
                    }

                    activityTimelineChart.data.labels = labels;
                    activityTimelineChart.data.datasets[0].data = incomingData;
                    activityTimelineChart.data.datasets[1].data = outgoingData;
                    activityTimelineChart.update();
                });
            });
        });
    </script>
}