/* _content/Murasalat/Views/Shared/_Layout.cshtml.rz.scp.css */
/* Please see documentation at https://learn.microsoft.com/aspnet/core/client-side/bundling-and-minification
for details on configuring this project to bundle and minify static web assets. */
body[b-37qfc36fy5] {
  font-family: 'Cairo', sans-serif;
  overflow-x: hidden;
  background-color: #f8f9fa;
}
.sidebar[b-37qfc36fy5] {
  height: 100vh;
  position: fixed;
  top: 0;
  right: 0;
  width: 250px;
  background-color: #ffffff;  /* Light background */
  padding-top: 0;
  transition: all 0.3s ease;
  z-index: 1000;
  box-shadow: 0 0 15px rgba(0,0,0,0.05);
  overflow: hidden;
}
.sidebar.collapsed[b-37qfc36fy5] {
  width: 70px;
}
.main-content[b-37qfc36fy5] {
  margin-right: 250px;
  transition: all 0.3s ease;
  padding: 20px;
}
.main-content.expanded[b-37qfc36fy5] {
  margin-right: 70px;
}
.nav-link[b-37qfc36fy5] {
  padding: 12px 20px;
  color: #333333;  /* Dark gray, almost black */
  display: flex;
  align-items: center;
  white-space: nowrap;
  border-radius: 5px;
  margin: 5px 10px;
  position: relative;
}
.nav-link:hover[b-37qfc36fy5] {
  background-color: #f8f9fa;  /* Light gray on hover */
  color: #000000;  /* Black text on hover */
}
.nav-link.active[b-37qfc36fy5] {
  background-color: #e9ecef;  /* Slightly darker background for active */
  color: #000000;  /* Black text for active state */
}
.nav-link i[b-37qfc36fy5] {
  width: 24px;
  text-align: center;
  margin-left: 10px;
  font-size: 1.1rem;
  color: #1b6ec2;  /* Gray icons */
}
.brand-container[b-37qfc36fy5] {
  padding: 20px;
  text-align: right;
  border-bottom: 1px solid #e9ecef;
  margin-bottom: 20px;
  color: #333333;  /* Dark text for header */
  background-color: #ffffff;  /* White background */
  display: flex;
  align-items: center;
  justify-content: space-between;
}
.toggle-btn[b-37qfc36fy5] {
  background: transparent;
  color: #666666;
  border: none;
  padding: 8px;
  cursor: pointer;
  margin-left: 10px;
  transition: all 0.3s ease;
}
.toggle-btn:hover[b-37qfc36fy5] {
  color: #000000;
}
.submenu[b-37qfc36fy5] {
  padding-right: 35px;
  background-color: #f8f9fa;  /* Light gray background */
  border-radius: 5px;
  margin: 0 10px;
}
.submenu .nav-link[b-37qfc36fy5] {
  color: #333333;  /* Dark gray text */
  margin: 2px 0;
}
.nav-link[aria-expanded="true"][b-37qfc36fy5] {
  color: #000000;  /* Black text for expanded menu */
  background-color: #f8f9fa;
}
.nav-link[aria-expanded="true"] i.fa-chevron-down[b-37qfc36fy5] {
  transform: rotate(180deg);
  transition: transform 0.3s ease;
}
.menu-text[b-37qfc36fy5] {
  transition: opacity 0.3s;
  font-size: 0.9rem;
}
.collapsed .menu-text[b-37qfc36fy5] {
  opacity: 0;
  display: none;
}
.collapsed .submenu[b-37qfc36fy5] {
  padding-right: 0;
  margin: 0;
}
.rotate-180[b-37qfc36fy5] {
  transform: rotate(180deg);
  transition: transform 0.3s ease;
}










