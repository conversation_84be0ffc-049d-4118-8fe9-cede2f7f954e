﻿@model AspCoreMVC6.Models.Author

@{
    ViewData["Title"] = "Details";
}

<h1>Details</h1>

<div>
    <h4>Author</h4>
    <hr />
    <dl class="row">
        <dt class = "col-sm-2">
            @Html.DisplayNameFor(model => model.AuthorName)
        </dt>
        <dd class = "col-sm-10">
            @Html.DisplayFor(model => model.AuthorName)
        </dd>
    </dl>
</div>
<div>
    <a asp-action="Edit" asp-route-id="@Model.AuthorId">Edit</a> |
    <a asp-action="Index">Back to List</a>
</div>
