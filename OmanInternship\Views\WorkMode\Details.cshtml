﻿@model OmanInternship.Models.WorkMode

@{
    ViewData["Title"] = "Details";
    Layout = "~/Views/Shared/_Layout.cshtml";
}

<h1>Details</h1>

<div>
    <h4>WorkMode</h4>
    <hr />
    <dl class="row">
        <dt class = "col-sm-2">
            @Html.DisplayNameFor(model => model.WorkModeName)
        </dt>
        <dd class = "col-sm-10">
            @Html.DisplayFor(model => model.WorkModeName)
        </dd>
    </dl>
</div>
<div>
    <a asp-action="Edit" asp-route-id="@Model?.WorkModeId">Edit</a> |
    <a asp-action="Index">Back to List</a>
</div>
