﻿@model AspCoreMVC6.Models.Book

@{
    ViewData["Title"] = "Details";
}

<h1>Details</h1>

<div>
    <h4>Book</h4>
    <hr />
    <dl class="row">
        <dt class = "col-sm-2">
            @Html.DisplayNameFor(model => model.Title)
        </dt>
        <dd class = "col-sm-10">
            @Html.DisplayFor(model => model.Title)
        </dd>
        <dt class = "col-sm-2">
            @Html.DisplayNameFor(model => model.Author)
        </dt>
        <dd class = "col-sm-10">
            @Html.DisplayFor(model => model.Author.AuthorName)
        </dd>
        <dt class = "col-sm-2">
            @Html.DisplayNameFor(model => model.Publisher)
        </dt>
        <dd class = "col-sm-10">
            @Html.DisplayFor(model => model.Publisher.PublisherName)
        </dd>
        <dt class = "col-sm-2">
            @Html.DisplayNameFor(model => model.Category)
        </dt>
        <dd class = "col-sm-10">
            @Html.DisplayFor(model => model.Category.Categoryname)
        </dd>
    </dl>
</div>
<div>
    <a asp-action="Edit" asp-route-id="@Model.Id">Edit</a> |
    <a asp-action="Index">Back to List</a>
</div>
