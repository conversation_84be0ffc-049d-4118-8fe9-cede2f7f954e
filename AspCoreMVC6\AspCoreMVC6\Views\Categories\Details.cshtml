﻿@model AspCoreMVC6.Models.Category

@{
    ViewData["Title"] = "Details";
}

<h1>Details</h1>

<div>
    <h4>Category</h4>
    <hr />
    <dl class="row">
        <dt class = "col-sm-2">
            @Html.DisplayNameFor(model => model.Categoryname)
        </dt>
        <dd class = "col-sm-10">
            @Html.DisplayFor(model => model.Categoryname)
        </dd>
    </dl>
</div>
<div>
    <a asp-action="Edit" asp-route-id="@Model.CategoryId">Edit</a> |
    <a asp-action="Index">Back to List</a>
</div>
