﻿@model IEnumerable<OmanInternship.ViewModel.ApplicationViewModel>

@{
    ViewData["Title"] = "Index";
    Layout = "~/Views/Shared/_Layout.cshtml";
}


<div class="container-fluid">
    <ejs-grid id="Grid" dataSource="@Model" width="100%" allowGrouping="true" allowSorting="true"
              allowReordering="true" allowPaging="true" gridLines="None"
              toolbar="@(new List<string>() { "Search" })">
        <e-grid-pagesettings pageSize="7"></e-grid-pagesettings>
        <e-grid-columns>
            <e-grid-column field="NIC" headerText="Student NIC" width="120"></e-grid-column>
            <e-grid-column field="Name" headerText="Name" width="150"></e-grid-column>
            <e-grid-column field="Gender" headerText="Gender" width="100"></e-grid-column>
            <e-grid-column field="University" headerText="University" width="150"></e-grid-column>
            <e-grid-column field="Country" headerText="Country of Study" width="150"></e-grid-column>
            <e-grid-column field="GPA" headerText="GPA" width="100"></e-grid-column>
            <e-grid-column field="SubmittedAt" headerText="Submitted At" width="150"></e-grid-column>
            <e-grid-column field="Status" headerText="Status" width="100"></e-grid-column>
            <e-grid-column headerText="Actions" template="#actionColumntemplate" width="150"></e-grid-column>
        </e-grid-columns>
    </ejs-grid>
</div>


@* this script for action button*@

<script id="actionColumntemplate" type="text/x-template">
    <a class="btn btn-primary btn-sm mx-1 " href="/Major/Details/${MajorId}">Details</a>
    <a class="btn btn-warning btn-sm mx-1 " href="/Major/Edit/${MajorId}">Edit</a>
    <a class="btn btn-danger btn-sm mx-1" href="/Major/Delete/${MajorId}">Delete</a>
</script>
