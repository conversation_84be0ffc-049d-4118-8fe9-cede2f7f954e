﻿@model IEnumerable<AspCoreMVC6.Models.Author>

@{
    ViewData["Title"] = "Index";
}

<h1>Index</h1>

<p>
    <a class="btn btn-outline-primary" asp-action="Create">إضافة مؤلف جديد </a>
</p>
<table class="table">
    <thead>
        <tr>
            <th>
                @Html.DisplayNameFor(model => model.AuthorName)
            </th>
            <th></th>
        </tr>
    </thead>
    <tbody>
@foreach (var item in Model) {
        <tr>
            <td>
                @Html.DisplayFor(modelItem => item.AuthorName)
            </td>
            <td class="" role="group">
                <a class="btn btn-primary" asp-action="Edit" asp-route-id="@item.AuthorId">تعديل</a> |
                <a class="btn btn-warning" asp-action="Details" asp-route-id="@item.AuthorId">التفاصيل</a> |
                <a class="btn btn-danger" asp-action="Delete" asp-route-id="@item.AuthorId">الحذف</a>
            </td>
        </tr>
}
    </tbody>
</table>
