{"Version": 1, "Hash": "tqfbdzvLh4grc9/HOZpd6SgBVbhR4zoQEcn5cdD/Gpk=", "Source": "AspCoreMVC6", "BasePath": "_content/AspCoreMVC6", "Mode": "<PERSON><PERSON><PERSON>", "ManifestType": "Build", "ReferencedProjectsConfiguration": [], "DiscoveryPatterns": [{"Name": "AspCoreMVC6\\wwwroot", "Source": "AspCoreMVC6", "ContentRoot": "C:\\Users\\<USER>\\source\\repos\\Almutasim90\\AspCoreMVC6\\AspCoreMVC6\\wwwroot\\", "BasePath": "_content/AspCoreMVC6", "Pattern": "**"}], "Assets": [{"Identity": "C:\\Users\\<USER>\\source\\repos\\Almutasim90\\AspCoreMVC6\\AspCoreMVC6\\obj\\Debug\\net6.0\\scopedcss\\bundle\\AspCoreMVC6.styles.css", "SourceId": "AspCoreMVC6", "SourceType": "Computed", "ContentRoot": "C:\\Users\\<USER>\\source\\repos\\Almutasim90\\AspCoreMVC6\\AspCoreMVC6\\obj\\Debug\\net6.0\\scopedcss\\bundle\\", "BasePath": "_content/AspCoreMVC6", "RelativePath": "AspCoreMVC6.styles.css", "AssetKind": "All", "AssetMode": "CurrentProject", "AssetRole": "Primary", "RelatedAsset": "", "AssetTraitName": "ScopedCss", "AssetTraitValue": "ApplicationBundle", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "C:\\Users\\<USER>\\source\\repos\\Almutasim90\\AspCoreMVC6\\AspCoreMVC6\\obj\\Debug\\net6.0\\scopedcss\\bundle\\AspCoreMVC6.styles.css"}, {"Identity": "C:\\Users\\<USER>\\source\\repos\\Almutasim90\\AspCoreMVC6\\AspCoreMVC6\\obj\\Debug\\net6.0\\scopedcss\\projectbundle\\AspCoreMVC6.bundle.scp.css", "SourceId": "AspCoreMVC6", "SourceType": "Computed", "ContentRoot": "C:\\Users\\<USER>\\source\\repos\\Almutasim90\\AspCoreMVC6\\AspCoreMVC6\\obj\\Debug\\net6.0\\scopedcss\\projectbundle\\", "BasePath": "_content/AspCoreMVC6", "RelativePath": "AspCoreMVC6.bundle.scp.css", "AssetKind": "All", "AssetMode": "Reference", "AssetRole": "Primary", "RelatedAsset": "", "AssetTraitName": "ScopedCss", "AssetTraitValue": "ProjectBundle", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "C:\\Users\\<USER>\\source\\repos\\Almutasim90\\AspCoreMVC6\\AspCoreMVC6\\obj\\Debug\\net6.0\\scopedcss\\projectbundle\\AspCoreMVC6.bundle.scp.css"}, {"Identity": "C:\\Users\\<USER>\\source\\repos\\Almutasim90\\AspCoreMVC6\\AspCoreMVC6\\wwwroot\\bootstrap\\css\\bootstrap.css", "SourceId": "AspCoreMVC6", "SourceType": "Discovered", "ContentRoot": "C:\\Users\\<USER>\\source\\repos\\Almutasim90\\AspCoreMVC6\\AspCoreMVC6\\wwwroot\\", "BasePath": "_content/AspCoreMVC6", "RelativePath": "bootstrap/css/bootstrap.css", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "RelatedAsset": "", "AssetTraitName": "", "AssetTraitValue": "", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\bootstrap\\css\\bootstrap.css"}, {"Identity": "C:\\Users\\<USER>\\source\\repos\\Almutasim90\\AspCoreMVC6\\AspCoreMVC6\\wwwroot\\bootstrap\\css\\bootstrap.css.map", "SourceId": "AspCoreMVC6", "SourceType": "Discovered", "ContentRoot": "C:\\Users\\<USER>\\source\\repos\\Almutasim90\\AspCoreMVC6\\AspCoreMVC6\\wwwroot\\", "BasePath": "_content/AspCoreMVC6", "RelativePath": "bootstrap/css/bootstrap.css.map", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "RelatedAsset": "", "AssetTraitName": "", "AssetTraitValue": "", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\bootstrap\\css\\bootstrap.css.map"}, {"Identity": "C:\\Users\\<USER>\\source\\repos\\Almutasim90\\AspCoreMVC6\\AspCoreMVC6\\wwwroot\\bootstrap\\css\\bootstrap.min.css", "SourceId": "AspCoreMVC6", "SourceType": "Discovered", "ContentRoot": "C:\\Users\\<USER>\\source\\repos\\Almutasim90\\AspCoreMVC6\\AspCoreMVC6\\wwwroot\\", "BasePath": "_content/AspCoreMVC6", "RelativePath": "bootstrap/css/bootstrap.min.css", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "RelatedAsset": "", "AssetTraitName": "", "AssetTraitValue": "", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\bootstrap\\css\\bootstrap.min.css"}, {"Identity": "C:\\Users\\<USER>\\source\\repos\\Almutasim90\\AspCoreMVC6\\AspCoreMVC6\\wwwroot\\bootstrap\\css\\bootstrap.min.css.map", "SourceId": "AspCoreMVC6", "SourceType": "Discovered", "ContentRoot": "C:\\Users\\<USER>\\source\\repos\\Almutasim90\\AspCoreMVC6\\AspCoreMVC6\\wwwroot\\", "BasePath": "_content/AspCoreMVC6", "RelativePath": "bootstrap/css/bootstrap.min.css.map", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "RelatedAsset": "", "AssetTraitName": "", "AssetTraitValue": "", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\bootstrap\\css\\bootstrap.min.css.map"}, {"Identity": "C:\\Users\\<USER>\\source\\repos\\Almutasim90\\AspCoreMVC6\\AspCoreMVC6\\wwwroot\\bootstrap\\css\\bootstrap.rtl.css", "SourceId": "AspCoreMVC6", "SourceType": "Discovered", "ContentRoot": "C:\\Users\\<USER>\\source\\repos\\Almutasim90\\AspCoreMVC6\\AspCoreMVC6\\wwwroot\\", "BasePath": "_content/AspCoreMVC6", "RelativePath": "bootstrap/css/bootstrap.rtl.css", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "RelatedAsset": "", "AssetTraitName": "", "AssetTraitValue": "", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\bootstrap\\css\\bootstrap.rtl.css"}, {"Identity": "C:\\Users\\<USER>\\source\\repos\\Almutasim90\\AspCoreMVC6\\AspCoreMVC6\\wwwroot\\bootstrap\\css\\bootstrap.rtl.css.map", "SourceId": "AspCoreMVC6", "SourceType": "Discovered", "ContentRoot": "C:\\Users\\<USER>\\source\\repos\\Almutasim90\\AspCoreMVC6\\AspCoreMVC6\\wwwroot\\", "BasePath": "_content/AspCoreMVC6", "RelativePath": "bootstrap/css/bootstrap.rtl.css.map", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "RelatedAsset": "", "AssetTraitName": "", "AssetTraitValue": "", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\bootstrap\\css\\bootstrap.rtl.css.map"}, {"Identity": "C:\\Users\\<USER>\\source\\repos\\Almutasim90\\AspCoreMVC6\\AspCoreMVC6\\wwwroot\\bootstrap\\css\\bootstrap.rtl.min.css", "SourceId": "AspCoreMVC6", "SourceType": "Discovered", "ContentRoot": "C:\\Users\\<USER>\\source\\repos\\Almutasim90\\AspCoreMVC6\\AspCoreMVC6\\wwwroot\\", "BasePath": "_content/AspCoreMVC6", "RelativePath": "bootstrap/css/bootstrap.rtl.min.css", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "RelatedAsset": "", "AssetTraitName": "", "AssetTraitValue": "", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\bootstrap\\css\\bootstrap.rtl.min.css"}, {"Identity": "C:\\Users\\<USER>\\source\\repos\\Almutasim90\\AspCoreMVC6\\AspCoreMVC6\\wwwroot\\bootstrap\\css\\bootstrap.rtl.min.css.map", "SourceId": "AspCoreMVC6", "SourceType": "Discovered", "ContentRoot": "C:\\Users\\<USER>\\source\\repos\\Almutasim90\\AspCoreMVC6\\AspCoreMVC6\\wwwroot\\", "BasePath": "_content/AspCoreMVC6", "RelativePath": "bootstrap/css/bootstrap.rtl.min.css.map", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "RelatedAsset": "", "AssetTraitName": "", "AssetTraitValue": "", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\bootstrap\\css\\bootstrap.rtl.min.css.map"}, {"Identity": "C:\\Users\\<USER>\\source\\repos\\Almutasim90\\AspCoreMVC6\\AspCoreMVC6\\wwwroot\\bootstrap\\css\\bootstrap-grid.css", "SourceId": "AspCoreMVC6", "SourceType": "Discovered", "ContentRoot": "C:\\Users\\<USER>\\source\\repos\\Almutasim90\\AspCoreMVC6\\AspCoreMVC6\\wwwroot\\", "BasePath": "_content/AspCoreMVC6", "RelativePath": "bootstrap/css/bootstrap-grid.css", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "RelatedAsset": "", "AssetTraitName": "", "AssetTraitValue": "", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\bootstrap\\css\\bootstrap-grid.css"}, {"Identity": "C:\\Users\\<USER>\\source\\repos\\Almutasim90\\AspCoreMVC6\\AspCoreMVC6\\wwwroot\\bootstrap\\css\\bootstrap-grid.css.map", "SourceId": "AspCoreMVC6", "SourceType": "Discovered", "ContentRoot": "C:\\Users\\<USER>\\source\\repos\\Almutasim90\\AspCoreMVC6\\AspCoreMVC6\\wwwroot\\", "BasePath": "_content/AspCoreMVC6", "RelativePath": "bootstrap/css/bootstrap-grid.css.map", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "RelatedAsset": "", "AssetTraitName": "", "AssetTraitValue": "", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\bootstrap\\css\\bootstrap-grid.css.map"}, {"Identity": "C:\\Users\\<USER>\\source\\repos\\Almutasim90\\AspCoreMVC6\\AspCoreMVC6\\wwwroot\\bootstrap\\css\\bootstrap-grid.min.css", "SourceId": "AspCoreMVC6", "SourceType": "Discovered", "ContentRoot": "C:\\Users\\<USER>\\source\\repos\\Almutasim90\\AspCoreMVC6\\AspCoreMVC6\\wwwroot\\", "BasePath": "_content/AspCoreMVC6", "RelativePath": "bootstrap/css/bootstrap-grid.min.css", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "RelatedAsset": "", "AssetTraitName": "", "AssetTraitValue": "", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\bootstrap\\css\\bootstrap-grid.min.css"}, {"Identity": "C:\\Users\\<USER>\\source\\repos\\Almutasim90\\AspCoreMVC6\\AspCoreMVC6\\wwwroot\\bootstrap\\css\\bootstrap-grid.min.css.map", "SourceId": "AspCoreMVC6", "SourceType": "Discovered", "ContentRoot": "C:\\Users\\<USER>\\source\\repos\\Almutasim90\\AspCoreMVC6\\AspCoreMVC6\\wwwroot\\", "BasePath": "_content/AspCoreMVC6", "RelativePath": "bootstrap/css/bootstrap-grid.min.css.map", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "RelatedAsset": "", "AssetTraitName": "", "AssetTraitValue": "", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\bootstrap\\css\\bootstrap-grid.min.css.map"}, {"Identity": "C:\\Users\\<USER>\\source\\repos\\Almutasim90\\AspCoreMVC6\\AspCoreMVC6\\wwwroot\\bootstrap\\css\\bootstrap-grid.rtl.css", "SourceId": "AspCoreMVC6", "SourceType": "Discovered", "ContentRoot": "C:\\Users\\<USER>\\source\\repos\\Almutasim90\\AspCoreMVC6\\AspCoreMVC6\\wwwroot\\", "BasePath": "_content/AspCoreMVC6", "RelativePath": "bootstrap/css/bootstrap-grid.rtl.css", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "RelatedAsset": "", "AssetTraitName": "", "AssetTraitValue": "", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\bootstrap\\css\\bootstrap-grid.rtl.css"}, {"Identity": "C:\\Users\\<USER>\\source\\repos\\Almutasim90\\AspCoreMVC6\\AspCoreMVC6\\wwwroot\\bootstrap\\css\\bootstrap-grid.rtl.css.map", "SourceId": "AspCoreMVC6", "SourceType": "Discovered", "ContentRoot": "C:\\Users\\<USER>\\source\\repos\\Almutasim90\\AspCoreMVC6\\AspCoreMVC6\\wwwroot\\", "BasePath": "_content/AspCoreMVC6", "RelativePath": "bootstrap/css/bootstrap-grid.rtl.css.map", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "RelatedAsset": "", "AssetTraitName": "", "AssetTraitValue": "", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\bootstrap\\css\\bootstrap-grid.rtl.css.map"}, {"Identity": "C:\\Users\\<USER>\\source\\repos\\Almutasim90\\AspCoreMVC6\\AspCoreMVC6\\wwwroot\\bootstrap\\css\\bootstrap-grid.rtl.min.css", "SourceId": "AspCoreMVC6", "SourceType": "Discovered", "ContentRoot": "C:\\Users\\<USER>\\source\\repos\\Almutasim90\\AspCoreMVC6\\AspCoreMVC6\\wwwroot\\", "BasePath": "_content/AspCoreMVC6", "RelativePath": "bootstrap/css/bootstrap-grid.rtl.min.css", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "RelatedAsset": "", "AssetTraitName": "", "AssetTraitValue": "", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\bootstrap\\css\\bootstrap-grid.rtl.min.css"}, {"Identity": "C:\\Users\\<USER>\\source\\repos\\Almutasim90\\AspCoreMVC6\\AspCoreMVC6\\wwwroot\\bootstrap\\css\\bootstrap-grid.rtl.min.css.map", "SourceId": "AspCoreMVC6", "SourceType": "Discovered", "ContentRoot": "C:\\Users\\<USER>\\source\\repos\\Almutasim90\\AspCoreMVC6\\AspCoreMVC6\\wwwroot\\", "BasePath": "_content/AspCoreMVC6", "RelativePath": "bootstrap/css/bootstrap-grid.rtl.min.css.map", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "RelatedAsset": "", "AssetTraitName": "", "AssetTraitValue": "", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\bootstrap\\css\\bootstrap-grid.rtl.min.css.map"}, {"Identity": "C:\\Users\\<USER>\\source\\repos\\Almutasim90\\AspCoreMVC6\\AspCoreMVC6\\wwwroot\\bootstrap\\css\\bootstrap-reboot.css", "SourceId": "AspCoreMVC6", "SourceType": "Discovered", "ContentRoot": "C:\\Users\\<USER>\\source\\repos\\Almutasim90\\AspCoreMVC6\\AspCoreMVC6\\wwwroot\\", "BasePath": "_content/AspCoreMVC6", "RelativePath": "bootstrap/css/bootstrap-reboot.css", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "RelatedAsset": "", "AssetTraitName": "", "AssetTraitValue": "", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\bootstrap\\css\\bootstrap-reboot.css"}, {"Identity": "C:\\Users\\<USER>\\source\\repos\\Almutasim90\\AspCoreMVC6\\AspCoreMVC6\\wwwroot\\bootstrap\\css\\bootstrap-reboot.css.map", "SourceId": "AspCoreMVC6", "SourceType": "Discovered", "ContentRoot": "C:\\Users\\<USER>\\source\\repos\\Almutasim90\\AspCoreMVC6\\AspCoreMVC6\\wwwroot\\", "BasePath": "_content/AspCoreMVC6", "RelativePath": "bootstrap/css/bootstrap-reboot.css.map", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "RelatedAsset": "", "AssetTraitName": "", "AssetTraitValue": "", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\bootstrap\\css\\bootstrap-reboot.css.map"}, {"Identity": "C:\\Users\\<USER>\\source\\repos\\Almutasim90\\AspCoreMVC6\\AspCoreMVC6\\wwwroot\\bootstrap\\css\\bootstrap-reboot.min.css", "SourceId": "AspCoreMVC6", "SourceType": "Discovered", "ContentRoot": "C:\\Users\\<USER>\\source\\repos\\Almutasim90\\AspCoreMVC6\\AspCoreMVC6\\wwwroot\\", "BasePath": "_content/AspCoreMVC6", "RelativePath": "bootstrap/css/bootstrap-reboot.min.css", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "RelatedAsset": "", "AssetTraitName": "", "AssetTraitValue": "", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\bootstrap\\css\\bootstrap-reboot.min.css"}, {"Identity": "C:\\Users\\<USER>\\source\\repos\\Almutasim90\\AspCoreMVC6\\AspCoreMVC6\\wwwroot\\bootstrap\\css\\bootstrap-reboot.min.css.map", "SourceId": "AspCoreMVC6", "SourceType": "Discovered", "ContentRoot": "C:\\Users\\<USER>\\source\\repos\\Almutasim90\\AspCoreMVC6\\AspCoreMVC6\\wwwroot\\", "BasePath": "_content/AspCoreMVC6", "RelativePath": "bootstrap/css/bootstrap-reboot.min.css.map", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "RelatedAsset": "", "AssetTraitName": "", "AssetTraitValue": "", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\bootstrap\\css\\bootstrap-reboot.min.css.map"}, {"Identity": "C:\\Users\\<USER>\\source\\repos\\Almutasim90\\AspCoreMVC6\\AspCoreMVC6\\wwwroot\\bootstrap\\css\\bootstrap-reboot.rtl.css", "SourceId": "AspCoreMVC6", "SourceType": "Discovered", "ContentRoot": "C:\\Users\\<USER>\\source\\repos\\Almutasim90\\AspCoreMVC6\\AspCoreMVC6\\wwwroot\\", "BasePath": "_content/AspCoreMVC6", "RelativePath": "bootstrap/css/bootstrap-reboot.rtl.css", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "RelatedAsset": "", "AssetTraitName": "", "AssetTraitValue": "", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\bootstrap\\css\\bootstrap-reboot.rtl.css"}, {"Identity": "C:\\Users\\<USER>\\source\\repos\\Almutasim90\\AspCoreMVC6\\AspCoreMVC6\\wwwroot\\bootstrap\\css\\bootstrap-reboot.rtl.css.map", "SourceId": "AspCoreMVC6", "SourceType": "Discovered", "ContentRoot": "C:\\Users\\<USER>\\source\\repos\\Almutasim90\\AspCoreMVC6\\AspCoreMVC6\\wwwroot\\", "BasePath": "_content/AspCoreMVC6", "RelativePath": "bootstrap/css/bootstrap-reboot.rtl.css.map", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "RelatedAsset": "", "AssetTraitName": "", "AssetTraitValue": "", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\bootstrap\\css\\bootstrap-reboot.rtl.css.map"}, {"Identity": "C:\\Users\\<USER>\\source\\repos\\Almutasim90\\AspCoreMVC6\\AspCoreMVC6\\wwwroot\\bootstrap\\css\\bootstrap-reboot.rtl.min.css", "SourceId": "AspCoreMVC6", "SourceType": "Discovered", "ContentRoot": "C:\\Users\\<USER>\\source\\repos\\Almutasim90\\AspCoreMVC6\\AspCoreMVC6\\wwwroot\\", "BasePath": "_content/AspCoreMVC6", "RelativePath": "bootstrap/css/bootstrap-reboot.rtl.min.css", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "RelatedAsset": "", "AssetTraitName": "", "AssetTraitValue": "", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\bootstrap\\css\\bootstrap-reboot.rtl.min.css"}, {"Identity": "C:\\Users\\<USER>\\source\\repos\\Almutasim90\\AspCoreMVC6\\AspCoreMVC6\\wwwroot\\bootstrap\\css\\bootstrap-reboot.rtl.min.css.map", "SourceId": "AspCoreMVC6", "SourceType": "Discovered", "ContentRoot": "C:\\Users\\<USER>\\source\\repos\\Almutasim90\\AspCoreMVC6\\AspCoreMVC6\\wwwroot\\", "BasePath": "_content/AspCoreMVC6", "RelativePath": "bootstrap/css/bootstrap-reboot.rtl.min.css.map", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "RelatedAsset": "", "AssetTraitName": "", "AssetTraitValue": "", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\bootstrap\\css\\bootstrap-reboot.rtl.min.css.map"}, {"Identity": "C:\\Users\\<USER>\\source\\repos\\Almutasim90\\AspCoreMVC6\\AspCoreMVC6\\wwwroot\\bootstrap\\css\\bootstrap-utilities.css", "SourceId": "AspCoreMVC6", "SourceType": "Discovered", "ContentRoot": "C:\\Users\\<USER>\\source\\repos\\Almutasim90\\AspCoreMVC6\\AspCoreMVC6\\wwwroot\\", "BasePath": "_content/AspCoreMVC6", "RelativePath": "bootstrap/css/bootstrap-utilities.css", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "RelatedAsset": "", "AssetTraitName": "", "AssetTraitValue": "", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\bootstrap\\css\\bootstrap-utilities.css"}, {"Identity": "C:\\Users\\<USER>\\source\\repos\\Almutasim90\\AspCoreMVC6\\AspCoreMVC6\\wwwroot\\bootstrap\\css\\bootstrap-utilities.css.map", "SourceId": "AspCoreMVC6", "SourceType": "Discovered", "ContentRoot": "C:\\Users\\<USER>\\source\\repos\\Almutasim90\\AspCoreMVC6\\AspCoreMVC6\\wwwroot\\", "BasePath": "_content/AspCoreMVC6", "RelativePath": "bootstrap/css/bootstrap-utilities.css.map", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "RelatedAsset": "", "AssetTraitName": "", "AssetTraitValue": "", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\bootstrap\\css\\bootstrap-utilities.css.map"}, {"Identity": "C:\\Users\\<USER>\\source\\repos\\Almutasim90\\AspCoreMVC6\\AspCoreMVC6\\wwwroot\\bootstrap\\css\\bootstrap-utilities.min.css", "SourceId": "AspCoreMVC6", "SourceType": "Discovered", "ContentRoot": "C:\\Users\\<USER>\\source\\repos\\Almutasim90\\AspCoreMVC6\\AspCoreMVC6\\wwwroot\\", "BasePath": "_content/AspCoreMVC6", "RelativePath": "bootstrap/css/bootstrap-utilities.min.css", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "RelatedAsset": "", "AssetTraitName": "", "AssetTraitValue": "", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\bootstrap\\css\\bootstrap-utilities.min.css"}, {"Identity": "C:\\Users\\<USER>\\source\\repos\\Almutasim90\\AspCoreMVC6\\AspCoreMVC6\\wwwroot\\bootstrap\\css\\bootstrap-utilities.min.css.map", "SourceId": "AspCoreMVC6", "SourceType": "Discovered", "ContentRoot": "C:\\Users\\<USER>\\source\\repos\\Almutasim90\\AspCoreMVC6\\AspCoreMVC6\\wwwroot\\", "BasePath": "_content/AspCoreMVC6", "RelativePath": "bootstrap/css/bootstrap-utilities.min.css.map", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "RelatedAsset": "", "AssetTraitName": "", "AssetTraitValue": "", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\bootstrap\\css\\bootstrap-utilities.min.css.map"}, {"Identity": "C:\\Users\\<USER>\\source\\repos\\Almutasim90\\AspCoreMVC6\\AspCoreMVC6\\wwwroot\\bootstrap\\css\\bootstrap-utilities.rtl.css", "SourceId": "AspCoreMVC6", "SourceType": "Discovered", "ContentRoot": "C:\\Users\\<USER>\\source\\repos\\Almutasim90\\AspCoreMVC6\\AspCoreMVC6\\wwwroot\\", "BasePath": "_content/AspCoreMVC6", "RelativePath": "bootstrap/css/bootstrap-utilities.rtl.css", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "RelatedAsset": "", "AssetTraitName": "", "AssetTraitValue": "", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\bootstrap\\css\\bootstrap-utilities.rtl.css"}, {"Identity": "C:\\Users\\<USER>\\source\\repos\\Almutasim90\\AspCoreMVC6\\AspCoreMVC6\\wwwroot\\bootstrap\\css\\bootstrap-utilities.rtl.css.map", "SourceId": "AspCoreMVC6", "SourceType": "Discovered", "ContentRoot": "C:\\Users\\<USER>\\source\\repos\\Almutasim90\\AspCoreMVC6\\AspCoreMVC6\\wwwroot\\", "BasePath": "_content/AspCoreMVC6", "RelativePath": "bootstrap/css/bootstrap-utilities.rtl.css.map", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "RelatedAsset": "", "AssetTraitName": "", "AssetTraitValue": "", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\bootstrap\\css\\bootstrap-utilities.rtl.css.map"}, {"Identity": "C:\\Users\\<USER>\\source\\repos\\Almutasim90\\AspCoreMVC6\\AspCoreMVC6\\wwwroot\\bootstrap\\css\\bootstrap-utilities.rtl.min.css", "SourceId": "AspCoreMVC6", "SourceType": "Discovered", "ContentRoot": "C:\\Users\\<USER>\\source\\repos\\Almutasim90\\AspCoreMVC6\\AspCoreMVC6\\wwwroot\\", "BasePath": "_content/AspCoreMVC6", "RelativePath": "bootstrap/css/bootstrap-utilities.rtl.min.css", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "RelatedAsset": "", "AssetTraitName": "", "AssetTraitValue": "", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\bootstrap\\css\\bootstrap-utilities.rtl.min.css"}, {"Identity": "C:\\Users\\<USER>\\source\\repos\\Almutasim90\\AspCoreMVC6\\AspCoreMVC6\\wwwroot\\bootstrap\\css\\bootstrap-utilities.rtl.min.css.map", "SourceId": "AspCoreMVC6", "SourceType": "Discovered", "ContentRoot": "C:\\Users\\<USER>\\source\\repos\\Almutasim90\\AspCoreMVC6\\AspCoreMVC6\\wwwroot\\", "BasePath": "_content/AspCoreMVC6", "RelativePath": "bootstrap/css/bootstrap-utilities.rtl.min.css.map", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "RelatedAsset": "", "AssetTraitName": "", "AssetTraitValue": "", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\bootstrap\\css\\bootstrap-utilities.rtl.min.css.map"}, {"Identity": "C:\\Users\\<USER>\\source\\repos\\Almutasim90\\AspCoreMVC6\\AspCoreMVC6\\wwwroot\\bootstrap\\js\\bootstrap.bundle.js", "SourceId": "AspCoreMVC6", "SourceType": "Discovered", "ContentRoot": "C:\\Users\\<USER>\\source\\repos\\Almutasim90\\AspCoreMVC6\\AspCoreMVC6\\wwwroot\\", "BasePath": "_content/AspCoreMVC6", "RelativePath": "bootstrap/js/bootstrap.bundle.js", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "RelatedAsset": "", "AssetTraitName": "", "AssetTraitValue": "", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\bootstrap\\js\\bootstrap.bundle.js"}, {"Identity": "C:\\Users\\<USER>\\source\\repos\\Almutasim90\\AspCoreMVC6\\AspCoreMVC6\\wwwroot\\bootstrap\\js\\bootstrap.bundle.js.map", "SourceId": "AspCoreMVC6", "SourceType": "Discovered", "ContentRoot": "C:\\Users\\<USER>\\source\\repos\\Almutasim90\\AspCoreMVC6\\AspCoreMVC6\\wwwroot\\", "BasePath": "_content/AspCoreMVC6", "RelativePath": "bootstrap/js/bootstrap.bundle.js.map", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "RelatedAsset": "", "AssetTraitName": "", "AssetTraitValue": "", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\bootstrap\\js\\bootstrap.bundle.js.map"}, {"Identity": "C:\\Users\\<USER>\\source\\repos\\Almutasim90\\AspCoreMVC6\\AspCoreMVC6\\wwwroot\\bootstrap\\js\\bootstrap.bundle.min.js", "SourceId": "AspCoreMVC6", "SourceType": "Discovered", "ContentRoot": "C:\\Users\\<USER>\\source\\repos\\Almutasim90\\AspCoreMVC6\\AspCoreMVC6\\wwwroot\\", "BasePath": "_content/AspCoreMVC6", "RelativePath": "bootstrap/js/bootstrap.bundle.min.js", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "RelatedAsset": "", "AssetTraitName": "", "AssetTraitValue": "", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\bootstrap\\js\\bootstrap.bundle.min.js"}, {"Identity": "C:\\Users\\<USER>\\source\\repos\\Almutasim90\\AspCoreMVC6\\AspCoreMVC6\\wwwroot\\bootstrap\\js\\bootstrap.bundle.min.js.map", "SourceId": "AspCoreMVC6", "SourceType": "Discovered", "ContentRoot": "C:\\Users\\<USER>\\source\\repos\\Almutasim90\\AspCoreMVC6\\AspCoreMVC6\\wwwroot\\", "BasePath": "_content/AspCoreMVC6", "RelativePath": "bootstrap/js/bootstrap.bundle.min.js.map", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "RelatedAsset": "", "AssetTraitName": "", "AssetTraitValue": "", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\bootstrap\\js\\bootstrap.bundle.min.js.map"}, {"Identity": "C:\\Users\\<USER>\\source\\repos\\Almutasim90\\AspCoreMVC6\\AspCoreMVC6\\wwwroot\\bootstrap\\js\\bootstrap.esm.js", "SourceId": "AspCoreMVC6", "SourceType": "Discovered", "ContentRoot": "C:\\Users\\<USER>\\source\\repos\\Almutasim90\\AspCoreMVC6\\AspCoreMVC6\\wwwroot\\", "BasePath": "_content/AspCoreMVC6", "RelativePath": "bootstrap/js/bootstrap.esm.js", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "RelatedAsset": "", "AssetTraitName": "", "AssetTraitValue": "", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\bootstrap\\js\\bootstrap.esm.js"}, {"Identity": "C:\\Users\\<USER>\\source\\repos\\Almutasim90\\AspCoreMVC6\\AspCoreMVC6\\wwwroot\\bootstrap\\js\\bootstrap.esm.js.map", "SourceId": "AspCoreMVC6", "SourceType": "Discovered", "ContentRoot": "C:\\Users\\<USER>\\source\\repos\\Almutasim90\\AspCoreMVC6\\AspCoreMVC6\\wwwroot\\", "BasePath": "_content/AspCoreMVC6", "RelativePath": "bootstrap/js/bootstrap.esm.js.map", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "RelatedAsset": "", "AssetTraitName": "", "AssetTraitValue": "", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\bootstrap\\js\\bootstrap.esm.js.map"}, {"Identity": "C:\\Users\\<USER>\\source\\repos\\Almutasim90\\AspCoreMVC6\\AspCoreMVC6\\wwwroot\\bootstrap\\js\\bootstrap.esm.min.js", "SourceId": "AspCoreMVC6", "SourceType": "Discovered", "ContentRoot": "C:\\Users\\<USER>\\source\\repos\\Almutasim90\\AspCoreMVC6\\AspCoreMVC6\\wwwroot\\", "BasePath": "_content/AspCoreMVC6", "RelativePath": "bootstrap/js/bootstrap.esm.min.js", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "RelatedAsset": "", "AssetTraitName": "", "AssetTraitValue": "", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\bootstrap\\js\\bootstrap.esm.min.js"}, {"Identity": "C:\\Users\\<USER>\\source\\repos\\Almutasim90\\AspCoreMVC6\\AspCoreMVC6\\wwwroot\\bootstrap\\js\\bootstrap.esm.min.js.map", "SourceId": "AspCoreMVC6", "SourceType": "Discovered", "ContentRoot": "C:\\Users\\<USER>\\source\\repos\\Almutasim90\\AspCoreMVC6\\AspCoreMVC6\\wwwroot\\", "BasePath": "_content/AspCoreMVC6", "RelativePath": "bootstrap/js/bootstrap.esm.min.js.map", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "RelatedAsset": "", "AssetTraitName": "", "AssetTraitValue": "", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\bootstrap\\js\\bootstrap.esm.min.js.map"}, {"Identity": "C:\\Users\\<USER>\\source\\repos\\Almutasim90\\AspCoreMVC6\\AspCoreMVC6\\wwwroot\\bootstrap\\js\\bootstrap.js", "SourceId": "AspCoreMVC6", "SourceType": "Discovered", "ContentRoot": "C:\\Users\\<USER>\\source\\repos\\Almutasim90\\AspCoreMVC6\\AspCoreMVC6\\wwwroot\\", "BasePath": "_content/AspCoreMVC6", "RelativePath": "bootstrap/js/bootstrap.js", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "RelatedAsset": "", "AssetTraitName": "", "AssetTraitValue": "", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\bootstrap\\js\\bootstrap.js"}, {"Identity": "C:\\Users\\<USER>\\source\\repos\\Almutasim90\\AspCoreMVC6\\AspCoreMVC6\\wwwroot\\bootstrap\\js\\bootstrap.js.map", "SourceId": "AspCoreMVC6", "SourceType": "Discovered", "ContentRoot": "C:\\Users\\<USER>\\source\\repos\\Almutasim90\\AspCoreMVC6\\AspCoreMVC6\\wwwroot\\", "BasePath": "_content/AspCoreMVC6", "RelativePath": "bootstrap/js/bootstrap.js.map", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "RelatedAsset": "", "AssetTraitName": "", "AssetTraitValue": "", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\bootstrap\\js\\bootstrap.js.map"}, {"Identity": "C:\\Users\\<USER>\\source\\repos\\Almutasim90\\AspCoreMVC6\\AspCoreMVC6\\wwwroot\\bootstrap\\js\\bootstrap.min.js", "SourceId": "AspCoreMVC6", "SourceType": "Discovered", "ContentRoot": "C:\\Users\\<USER>\\source\\repos\\Almutasim90\\AspCoreMVC6\\AspCoreMVC6\\wwwroot\\", "BasePath": "_content/AspCoreMVC6", "RelativePath": "bootstrap/js/bootstrap.min.js", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "RelatedAsset": "", "AssetTraitName": "", "AssetTraitValue": "", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\bootstrap\\js\\bootstrap.min.js"}, {"Identity": "C:\\Users\\<USER>\\source\\repos\\Almutasim90\\AspCoreMVC6\\AspCoreMVC6\\wwwroot\\bootstrap\\js\\bootstrap.min.js.map", "SourceId": "AspCoreMVC6", "SourceType": "Discovered", "ContentRoot": "C:\\Users\\<USER>\\source\\repos\\Almutasim90\\AspCoreMVC6\\AspCoreMVC6\\wwwroot\\", "BasePath": "_content/AspCoreMVC6", "RelativePath": "bootstrap/js/bootstrap.min.js.map", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "RelatedAsset": "", "AssetTraitName": "", "AssetTraitValue": "", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\bootstrap\\js\\bootstrap.min.js.map"}, {"Identity": "C:\\Users\\<USER>\\source\\repos\\Almutasim90\\AspCoreMVC6\\AspCoreMVC6\\wwwroot\\bootstrap\\scss\\_accordion.scss", "SourceId": "AspCoreMVC6", "SourceType": "Discovered", "ContentRoot": "C:\\Users\\<USER>\\source\\repos\\Almutasim90\\AspCoreMVC6\\AspCoreMVC6\\wwwroot\\", "BasePath": "_content/AspCoreMVC6", "RelativePath": "bootstrap/scss/_accordion.scss", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "RelatedAsset": "", "AssetTraitName": "", "AssetTraitValue": "", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\bootstrap\\scss\\_accordion.scss"}, {"Identity": "C:\\Users\\<USER>\\source\\repos\\Almutasim90\\AspCoreMVC6\\AspCoreMVC6\\wwwroot\\bootstrap\\scss\\_alert.scss", "SourceId": "AspCoreMVC6", "SourceType": "Discovered", "ContentRoot": "C:\\Users\\<USER>\\source\\repos\\Almutasim90\\AspCoreMVC6\\AspCoreMVC6\\wwwroot\\", "BasePath": "_content/AspCoreMVC6", "RelativePath": "bootstrap/scss/_alert.scss", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "RelatedAsset": "", "AssetTraitName": "", "AssetTraitValue": "", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\bootstrap\\scss\\_alert.scss"}, {"Identity": "C:\\Users\\<USER>\\source\\repos\\Almutasim90\\AspCoreMVC6\\AspCoreMVC6\\wwwroot\\bootstrap\\scss\\_badge.scss", "SourceId": "AspCoreMVC6", "SourceType": "Discovered", "ContentRoot": "C:\\Users\\<USER>\\source\\repos\\Almutasim90\\AspCoreMVC6\\AspCoreMVC6\\wwwroot\\", "BasePath": "_content/AspCoreMVC6", "RelativePath": "bootstrap/scss/_badge.scss", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "RelatedAsset": "", "AssetTraitName": "", "AssetTraitValue": "", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\bootstrap\\scss\\_badge.scss"}, {"Identity": "C:\\Users\\<USER>\\source\\repos\\Almutasim90\\AspCoreMVC6\\AspCoreMVC6\\wwwroot\\bootstrap\\scss\\_breadcrumb.scss", "SourceId": "AspCoreMVC6", "SourceType": "Discovered", "ContentRoot": "C:\\Users\\<USER>\\source\\repos\\Almutasim90\\AspCoreMVC6\\AspCoreMVC6\\wwwroot\\", "BasePath": "_content/AspCoreMVC6", "RelativePath": "bootstrap/scss/_breadcrumb.scss", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "RelatedAsset": "", "AssetTraitName": "", "AssetTraitValue": "", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\bootstrap\\scss\\_breadcrumb.scss"}, {"Identity": "C:\\Users\\<USER>\\source\\repos\\Almutasim90\\AspCoreMVC6\\AspCoreMVC6\\wwwroot\\bootstrap\\scss\\_button-group.scss", "SourceId": "AspCoreMVC6", "SourceType": "Discovered", "ContentRoot": "C:\\Users\\<USER>\\source\\repos\\Almutasim90\\AspCoreMVC6\\AspCoreMVC6\\wwwroot\\", "BasePath": "_content/AspCoreMVC6", "RelativePath": "bootstrap/scss/_button-group.scss", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "RelatedAsset": "", "AssetTraitName": "", "AssetTraitValue": "", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\bootstrap\\scss\\_button-group.scss"}, {"Identity": "C:\\Users\\<USER>\\source\\repos\\Almutasim90\\AspCoreMVC6\\AspCoreMVC6\\wwwroot\\bootstrap\\scss\\_buttons.scss", "SourceId": "AspCoreMVC6", "SourceType": "Discovered", "ContentRoot": "C:\\Users\\<USER>\\source\\repos\\Almutasim90\\AspCoreMVC6\\AspCoreMVC6\\wwwroot\\", "BasePath": "_content/AspCoreMVC6", "RelativePath": "bootstrap/scss/_buttons.scss", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "RelatedAsset": "", "AssetTraitName": "", "AssetTraitValue": "", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\bootstrap\\scss\\_buttons.scss"}, {"Identity": "C:\\Users\\<USER>\\source\\repos\\Almutasim90\\AspCoreMVC6\\AspCoreMVC6\\wwwroot\\bootstrap\\scss\\_card.scss", "SourceId": "AspCoreMVC6", "SourceType": "Discovered", "ContentRoot": "C:\\Users\\<USER>\\source\\repos\\Almutasim90\\AspCoreMVC6\\AspCoreMVC6\\wwwroot\\", "BasePath": "_content/AspCoreMVC6", "RelativePath": "bootstrap/scss/_card.scss", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "RelatedAsset": "", "AssetTraitName": "", "AssetTraitValue": "", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\bootstrap\\scss\\_card.scss"}, {"Identity": "C:\\Users\\<USER>\\source\\repos\\Almutasim90\\AspCoreMVC6\\AspCoreMVC6\\wwwroot\\bootstrap\\scss\\_carousel.scss", "SourceId": "AspCoreMVC6", "SourceType": "Discovered", "ContentRoot": "C:\\Users\\<USER>\\source\\repos\\Almutasim90\\AspCoreMVC6\\AspCoreMVC6\\wwwroot\\", "BasePath": "_content/AspCoreMVC6", "RelativePath": "bootstrap/scss/_carousel.scss", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "RelatedAsset": "", "AssetTraitName": "", "AssetTraitValue": "", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\bootstrap\\scss\\_carousel.scss"}, {"Identity": "C:\\Users\\<USER>\\source\\repos\\Almutasim90\\AspCoreMVC6\\AspCoreMVC6\\wwwroot\\bootstrap\\scss\\_close.scss", "SourceId": "AspCoreMVC6", "SourceType": "Discovered", "ContentRoot": "C:\\Users\\<USER>\\source\\repos\\Almutasim90\\AspCoreMVC6\\AspCoreMVC6\\wwwroot\\", "BasePath": "_content/AspCoreMVC6", "RelativePath": "bootstrap/scss/_close.scss", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "RelatedAsset": "", "AssetTraitName": "", "AssetTraitValue": "", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\bootstrap\\scss\\_close.scss"}, {"Identity": "C:\\Users\\<USER>\\source\\repos\\Almutasim90\\AspCoreMVC6\\AspCoreMVC6\\wwwroot\\bootstrap\\scss\\_containers.scss", "SourceId": "AspCoreMVC6", "SourceType": "Discovered", "ContentRoot": "C:\\Users\\<USER>\\source\\repos\\Almutasim90\\AspCoreMVC6\\AspCoreMVC6\\wwwroot\\", "BasePath": "_content/AspCoreMVC6", "RelativePath": "bootstrap/scss/_containers.scss", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "RelatedAsset": "", "AssetTraitName": "", "AssetTraitValue": "", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\bootstrap\\scss\\_containers.scss"}, {"Identity": "C:\\Users\\<USER>\\source\\repos\\Almutasim90\\AspCoreMVC6\\AspCoreMVC6\\wwwroot\\bootstrap\\scss\\_dropdown.scss", "SourceId": "AspCoreMVC6", "SourceType": "Discovered", "ContentRoot": "C:\\Users\\<USER>\\source\\repos\\Almutasim90\\AspCoreMVC6\\AspCoreMVC6\\wwwroot\\", "BasePath": "_content/AspCoreMVC6", "RelativePath": "bootstrap/scss/_dropdown.scss", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "RelatedAsset": "", "AssetTraitName": "", "AssetTraitValue": "", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\bootstrap\\scss\\_dropdown.scss"}, {"Identity": "C:\\Users\\<USER>\\source\\repos\\Almutasim90\\AspCoreMVC6\\AspCoreMVC6\\wwwroot\\bootstrap\\scss\\_forms.scss", "SourceId": "AspCoreMVC6", "SourceType": "Discovered", "ContentRoot": "C:\\Users\\<USER>\\source\\repos\\Almutasim90\\AspCoreMVC6\\AspCoreMVC6\\wwwroot\\", "BasePath": "_content/AspCoreMVC6", "RelativePath": "bootstrap/scss/_forms.scss", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "RelatedAsset": "", "AssetTraitName": "", "AssetTraitValue": "", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\bootstrap\\scss\\_forms.scss"}, {"Identity": "C:\\Users\\<USER>\\source\\repos\\Almutasim90\\AspCoreMVC6\\AspCoreMVC6\\wwwroot\\bootstrap\\scss\\_functions.scss", "SourceId": "AspCoreMVC6", "SourceType": "Discovered", "ContentRoot": "C:\\Users\\<USER>\\source\\repos\\Almutasim90\\AspCoreMVC6\\AspCoreMVC6\\wwwroot\\", "BasePath": "_content/AspCoreMVC6", "RelativePath": "bootstrap/scss/_functions.scss", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "RelatedAsset": "", "AssetTraitName": "", "AssetTraitValue": "", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\bootstrap\\scss\\_functions.scss"}, {"Identity": "C:\\Users\\<USER>\\source\\repos\\Almutasim90\\AspCoreMVC6\\AspCoreMVC6\\wwwroot\\bootstrap\\scss\\_grid.scss", "SourceId": "AspCoreMVC6", "SourceType": "Discovered", "ContentRoot": "C:\\Users\\<USER>\\source\\repos\\Almutasim90\\AspCoreMVC6\\AspCoreMVC6\\wwwroot\\", "BasePath": "_content/AspCoreMVC6", "RelativePath": "bootstrap/scss/_grid.scss", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "RelatedAsset": "", "AssetTraitName": "", "AssetTraitValue": "", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\bootstrap\\scss\\_grid.scss"}, {"Identity": "C:\\Users\\<USER>\\source\\repos\\Almutasim90\\AspCoreMVC6\\AspCoreMVC6\\wwwroot\\bootstrap\\scss\\_helpers.scss", "SourceId": "AspCoreMVC6", "SourceType": "Discovered", "ContentRoot": "C:\\Users\\<USER>\\source\\repos\\Almutasim90\\AspCoreMVC6\\AspCoreMVC6\\wwwroot\\", "BasePath": "_content/AspCoreMVC6", "RelativePath": "bootstrap/scss/_helpers.scss", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "RelatedAsset": "", "AssetTraitName": "", "AssetTraitValue": "", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\bootstrap\\scss\\_helpers.scss"}, {"Identity": "C:\\Users\\<USER>\\source\\repos\\Almutasim90\\AspCoreMVC6\\AspCoreMVC6\\wwwroot\\bootstrap\\scss\\_images.scss", "SourceId": "AspCoreMVC6", "SourceType": "Discovered", "ContentRoot": "C:\\Users\\<USER>\\source\\repos\\Almutasim90\\AspCoreMVC6\\AspCoreMVC6\\wwwroot\\", "BasePath": "_content/AspCoreMVC6", "RelativePath": "bootstrap/scss/_images.scss", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "RelatedAsset": "", "AssetTraitName": "", "AssetTraitValue": "", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\bootstrap\\scss\\_images.scss"}, {"Identity": "C:\\Users\\<USER>\\source\\repos\\Almutasim90\\AspCoreMVC6\\AspCoreMVC6\\wwwroot\\bootstrap\\scss\\_list-group.scss", "SourceId": "AspCoreMVC6", "SourceType": "Discovered", "ContentRoot": "C:\\Users\\<USER>\\source\\repos\\Almutasim90\\AspCoreMVC6\\AspCoreMVC6\\wwwroot\\", "BasePath": "_content/AspCoreMVC6", "RelativePath": "bootstrap/scss/_list-group.scss", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "RelatedAsset": "", "AssetTraitName": "", "AssetTraitValue": "", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\bootstrap\\scss\\_list-group.scss"}, {"Identity": "C:\\Users\\<USER>\\source\\repos\\Almutasim90\\AspCoreMVC6\\AspCoreMVC6\\wwwroot\\bootstrap\\scss\\_maps.scss", "SourceId": "AspCoreMVC6", "SourceType": "Discovered", "ContentRoot": "C:\\Users\\<USER>\\source\\repos\\Almutasim90\\AspCoreMVC6\\AspCoreMVC6\\wwwroot\\", "BasePath": "_content/AspCoreMVC6", "RelativePath": "bootstrap/scss/_maps.scss", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "RelatedAsset": "", "AssetTraitName": "", "AssetTraitValue": "", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\bootstrap\\scss\\_maps.scss"}, {"Identity": "C:\\Users\\<USER>\\source\\repos\\Almutasim90\\AspCoreMVC6\\AspCoreMVC6\\wwwroot\\bootstrap\\scss\\_mixins.scss", "SourceId": "AspCoreMVC6", "SourceType": "Discovered", "ContentRoot": "C:\\Users\\<USER>\\source\\repos\\Almutasim90\\AspCoreMVC6\\AspCoreMVC6\\wwwroot\\", "BasePath": "_content/AspCoreMVC6", "RelativePath": "bootstrap/scss/_mixins.scss", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "RelatedAsset": "", "AssetTraitName": "", "AssetTraitValue": "", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\bootstrap\\scss\\_mixins.scss"}, {"Identity": "C:\\Users\\<USER>\\source\\repos\\Almutasim90\\AspCoreMVC6\\AspCoreMVC6\\wwwroot\\bootstrap\\scss\\_modal.scss", "SourceId": "AspCoreMVC6", "SourceType": "Discovered", "ContentRoot": "C:\\Users\\<USER>\\source\\repos\\Almutasim90\\AspCoreMVC6\\AspCoreMVC6\\wwwroot\\", "BasePath": "_content/AspCoreMVC6", "RelativePath": "bootstrap/scss/_modal.scss", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "RelatedAsset": "", "AssetTraitName": "", "AssetTraitValue": "", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\bootstrap\\scss\\_modal.scss"}, {"Identity": "C:\\Users\\<USER>\\source\\repos\\Almutasim90\\AspCoreMVC6\\AspCoreMVC6\\wwwroot\\bootstrap\\scss\\_nav.scss", "SourceId": "AspCoreMVC6", "SourceType": "Discovered", "ContentRoot": "C:\\Users\\<USER>\\source\\repos\\Almutasim90\\AspCoreMVC6\\AspCoreMVC6\\wwwroot\\", "BasePath": "_content/AspCoreMVC6", "RelativePath": "bootstrap/scss/_nav.scss", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "RelatedAsset": "", "AssetTraitName": "", "AssetTraitValue": "", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\bootstrap\\scss\\_nav.scss"}, {"Identity": "C:\\Users\\<USER>\\source\\repos\\Almutasim90\\AspCoreMVC6\\AspCoreMVC6\\wwwroot\\bootstrap\\scss\\_navbar.scss", "SourceId": "AspCoreMVC6", "SourceType": "Discovered", "ContentRoot": "C:\\Users\\<USER>\\source\\repos\\Almutasim90\\AspCoreMVC6\\AspCoreMVC6\\wwwroot\\", "BasePath": "_content/AspCoreMVC6", "RelativePath": "bootstrap/scss/_navbar.scss", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "RelatedAsset": "", "AssetTraitName": "", "AssetTraitValue": "", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\bootstrap\\scss\\_navbar.scss"}, {"Identity": "C:\\Users\\<USER>\\source\\repos\\Almutasim90\\AspCoreMVC6\\AspCoreMVC6\\wwwroot\\bootstrap\\scss\\_offcanvas.scss", "SourceId": "AspCoreMVC6", "SourceType": "Discovered", "ContentRoot": "C:\\Users\\<USER>\\source\\repos\\Almutasim90\\AspCoreMVC6\\AspCoreMVC6\\wwwroot\\", "BasePath": "_content/AspCoreMVC6", "RelativePath": "bootstrap/scss/_offcanvas.scss", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "RelatedAsset": "", "AssetTraitName": "", "AssetTraitValue": "", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\bootstrap\\scss\\_offcanvas.scss"}, {"Identity": "C:\\Users\\<USER>\\source\\repos\\Almutasim90\\AspCoreMVC6\\AspCoreMVC6\\wwwroot\\bootstrap\\scss\\_pagination.scss", "SourceId": "AspCoreMVC6", "SourceType": "Discovered", "ContentRoot": "C:\\Users\\<USER>\\source\\repos\\Almutasim90\\AspCoreMVC6\\AspCoreMVC6\\wwwroot\\", "BasePath": "_content/AspCoreMVC6", "RelativePath": "bootstrap/scss/_pagination.scss", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "RelatedAsset": "", "AssetTraitName": "", "AssetTraitValue": "", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\bootstrap\\scss\\_pagination.scss"}, {"Identity": "C:\\Users\\<USER>\\source\\repos\\Almutasim90\\AspCoreMVC6\\AspCoreMVC6\\wwwroot\\bootstrap\\scss\\_placeholders.scss", "SourceId": "AspCoreMVC6", "SourceType": "Discovered", "ContentRoot": "C:\\Users\\<USER>\\source\\repos\\Almutasim90\\AspCoreMVC6\\AspCoreMVC6\\wwwroot\\", "BasePath": "_content/AspCoreMVC6", "RelativePath": "bootstrap/scss/_placeholders.scss", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "RelatedAsset": "", "AssetTraitName": "", "AssetTraitValue": "", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\bootstrap\\scss\\_placeholders.scss"}, {"Identity": "C:\\Users\\<USER>\\source\\repos\\Almutasim90\\AspCoreMVC6\\AspCoreMVC6\\wwwroot\\bootstrap\\scss\\_popover.scss", "SourceId": "AspCoreMVC6", "SourceType": "Discovered", "ContentRoot": "C:\\Users\\<USER>\\source\\repos\\Almutasim90\\AspCoreMVC6\\AspCoreMVC6\\wwwroot\\", "BasePath": "_content/AspCoreMVC6", "RelativePath": "bootstrap/scss/_popover.scss", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "RelatedAsset": "", "AssetTraitName": "", "AssetTraitValue": "", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\bootstrap\\scss\\_popover.scss"}, {"Identity": "C:\\Users\\<USER>\\source\\repos\\Almutasim90\\AspCoreMVC6\\AspCoreMVC6\\wwwroot\\bootstrap\\scss\\_progress.scss", "SourceId": "AspCoreMVC6", "SourceType": "Discovered", "ContentRoot": "C:\\Users\\<USER>\\source\\repos\\Almutasim90\\AspCoreMVC6\\AspCoreMVC6\\wwwroot\\", "BasePath": "_content/AspCoreMVC6", "RelativePath": "bootstrap/scss/_progress.scss", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "RelatedAsset": "", "AssetTraitName": "", "AssetTraitValue": "", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\bootstrap\\scss\\_progress.scss"}, {"Identity": "C:\\Users\\<USER>\\source\\repos\\Almutasim90\\AspCoreMVC6\\AspCoreMVC6\\wwwroot\\bootstrap\\scss\\_reboot.scss", "SourceId": "AspCoreMVC6", "SourceType": "Discovered", "ContentRoot": "C:\\Users\\<USER>\\source\\repos\\Almutasim90\\AspCoreMVC6\\AspCoreMVC6\\wwwroot\\", "BasePath": "_content/AspCoreMVC6", "RelativePath": "bootstrap/scss/_reboot.scss", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "RelatedAsset": "", "AssetTraitName": "", "AssetTraitValue": "", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\bootstrap\\scss\\_reboot.scss"}, {"Identity": "C:\\Users\\<USER>\\source\\repos\\Almutasim90\\AspCoreMVC6\\AspCoreMVC6\\wwwroot\\bootstrap\\scss\\_root.scss", "SourceId": "AspCoreMVC6", "SourceType": "Discovered", "ContentRoot": "C:\\Users\\<USER>\\source\\repos\\Almutasim90\\AspCoreMVC6\\AspCoreMVC6\\wwwroot\\", "BasePath": "_content/AspCoreMVC6", "RelativePath": "bootstrap/scss/_root.scss", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "RelatedAsset": "", "AssetTraitName": "", "AssetTraitValue": "", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\bootstrap\\scss\\_root.scss"}, {"Identity": "C:\\Users\\<USER>\\source\\repos\\Almutasim90\\AspCoreMVC6\\AspCoreMVC6\\wwwroot\\bootstrap\\scss\\_spinners.scss", "SourceId": "AspCoreMVC6", "SourceType": "Discovered", "ContentRoot": "C:\\Users\\<USER>\\source\\repos\\Almutasim90\\AspCoreMVC6\\AspCoreMVC6\\wwwroot\\", "BasePath": "_content/AspCoreMVC6", "RelativePath": "bootstrap/scss/_spinners.scss", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "RelatedAsset": "", "AssetTraitName": "", "AssetTraitValue": "", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\bootstrap\\scss\\_spinners.scss"}, {"Identity": "C:\\Users\\<USER>\\source\\repos\\Almutasim90\\AspCoreMVC6\\AspCoreMVC6\\wwwroot\\bootstrap\\scss\\_tables.scss", "SourceId": "AspCoreMVC6", "SourceType": "Discovered", "ContentRoot": "C:\\Users\\<USER>\\source\\repos\\Almutasim90\\AspCoreMVC6\\AspCoreMVC6\\wwwroot\\", "BasePath": "_content/AspCoreMVC6", "RelativePath": "bootstrap/scss/_tables.scss", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "RelatedAsset": "", "AssetTraitName": "", "AssetTraitValue": "", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\bootstrap\\scss\\_tables.scss"}, {"Identity": "C:\\Users\\<USER>\\source\\repos\\Almutasim90\\AspCoreMVC6\\AspCoreMVC6\\wwwroot\\bootstrap\\scss\\_toasts.scss", "SourceId": "AspCoreMVC6", "SourceType": "Discovered", "ContentRoot": "C:\\Users\\<USER>\\source\\repos\\Almutasim90\\AspCoreMVC6\\AspCoreMVC6\\wwwroot\\", "BasePath": "_content/AspCoreMVC6", "RelativePath": "bootstrap/scss/_toasts.scss", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "RelatedAsset": "", "AssetTraitName": "", "AssetTraitValue": "", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\bootstrap\\scss\\_toasts.scss"}, {"Identity": "C:\\Users\\<USER>\\source\\repos\\Almutasim90\\AspCoreMVC6\\AspCoreMVC6\\wwwroot\\bootstrap\\scss\\_tooltip.scss", "SourceId": "AspCoreMVC6", "SourceType": "Discovered", "ContentRoot": "C:\\Users\\<USER>\\source\\repos\\Almutasim90\\AspCoreMVC6\\AspCoreMVC6\\wwwroot\\", "BasePath": "_content/AspCoreMVC6", "RelativePath": "bootstrap/scss/_tooltip.scss", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "RelatedAsset": "", "AssetTraitName": "", "AssetTraitValue": "", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\bootstrap\\scss\\_tooltip.scss"}, {"Identity": "C:\\Users\\<USER>\\source\\repos\\Almutasim90\\AspCoreMVC6\\AspCoreMVC6\\wwwroot\\bootstrap\\scss\\_transitions.scss", "SourceId": "AspCoreMVC6", "SourceType": "Discovered", "ContentRoot": "C:\\Users\\<USER>\\source\\repos\\Almutasim90\\AspCoreMVC6\\AspCoreMVC6\\wwwroot\\", "BasePath": "_content/AspCoreMVC6", "RelativePath": "bootstrap/scss/_transitions.scss", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "RelatedAsset": "", "AssetTraitName": "", "AssetTraitValue": "", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\bootstrap\\scss\\_transitions.scss"}, {"Identity": "C:\\Users\\<USER>\\source\\repos\\Almutasim90\\AspCoreMVC6\\AspCoreMVC6\\wwwroot\\bootstrap\\scss\\_type.scss", "SourceId": "AspCoreMVC6", "SourceType": "Discovered", "ContentRoot": "C:\\Users\\<USER>\\source\\repos\\Almutasim90\\AspCoreMVC6\\AspCoreMVC6\\wwwroot\\", "BasePath": "_content/AspCoreMVC6", "RelativePath": "bootstrap/scss/_type.scss", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "RelatedAsset": "", "AssetTraitName": "", "AssetTraitValue": "", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\bootstrap\\scss\\_type.scss"}, {"Identity": "C:\\Users\\<USER>\\source\\repos\\Almutasim90\\AspCoreMVC6\\AspCoreMVC6\\wwwroot\\bootstrap\\scss\\_utilities.scss", "SourceId": "AspCoreMVC6", "SourceType": "Discovered", "ContentRoot": "C:\\Users\\<USER>\\source\\repos\\Almutasim90\\AspCoreMVC6\\AspCoreMVC6\\wwwroot\\", "BasePath": "_content/AspCoreMVC6", "RelativePath": "bootstrap/scss/_utilities.scss", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "RelatedAsset": "", "AssetTraitName": "", "AssetTraitValue": "", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\bootstrap\\scss\\_utilities.scss"}, {"Identity": "C:\\Users\\<USER>\\source\\repos\\Almutasim90\\AspCoreMVC6\\AspCoreMVC6\\wwwroot\\bootstrap\\scss\\_variables.scss", "SourceId": "AspCoreMVC6", "SourceType": "Discovered", "ContentRoot": "C:\\Users\\<USER>\\source\\repos\\Almutasim90\\AspCoreMVC6\\AspCoreMVC6\\wwwroot\\", "BasePath": "_content/AspCoreMVC6", "RelativePath": "bootstrap/scss/_variables.scss", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "RelatedAsset": "", "AssetTraitName": "", "AssetTraitValue": "", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\bootstrap\\scss\\_variables.scss"}, {"Identity": "C:\\Users\\<USER>\\source\\repos\\Almutasim90\\AspCoreMVC6\\AspCoreMVC6\\wwwroot\\bootstrap\\scss\\bootstrap.scss", "SourceId": "AspCoreMVC6", "SourceType": "Discovered", "ContentRoot": "C:\\Users\\<USER>\\source\\repos\\Almutasim90\\AspCoreMVC6\\AspCoreMVC6\\wwwroot\\", "BasePath": "_content/AspCoreMVC6", "RelativePath": "bootstrap/scss/bootstrap.scss", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "RelatedAsset": "", "AssetTraitName": "", "AssetTraitValue": "", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\bootstrap\\scss\\bootstrap.scss"}, {"Identity": "C:\\Users\\<USER>\\source\\repos\\Almutasim90\\AspCoreMVC6\\AspCoreMVC6\\wwwroot\\bootstrap\\scss\\bootstrap-grid.scss", "SourceId": "AspCoreMVC6", "SourceType": "Discovered", "ContentRoot": "C:\\Users\\<USER>\\source\\repos\\Almutasim90\\AspCoreMVC6\\AspCoreMVC6\\wwwroot\\", "BasePath": "_content/AspCoreMVC6", "RelativePath": "bootstrap/scss/bootstrap-grid.scss", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "RelatedAsset": "", "AssetTraitName": "", "AssetTraitValue": "", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\bootstrap\\scss\\bootstrap-grid.scss"}, {"Identity": "C:\\Users\\<USER>\\source\\repos\\Almutasim90\\AspCoreMVC6\\AspCoreMVC6\\wwwroot\\bootstrap\\scss\\bootstrap-reboot.scss", "SourceId": "AspCoreMVC6", "SourceType": "Discovered", "ContentRoot": "C:\\Users\\<USER>\\source\\repos\\Almutasim90\\AspCoreMVC6\\AspCoreMVC6\\wwwroot\\", "BasePath": "_content/AspCoreMVC6", "RelativePath": "bootstrap/scss/bootstrap-reboot.scss", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "RelatedAsset": "", "AssetTraitName": "", "AssetTraitValue": "", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\bootstrap\\scss\\bootstrap-reboot.scss"}, {"Identity": "C:\\Users\\<USER>\\source\\repos\\Almutasim90\\AspCoreMVC6\\AspCoreMVC6\\wwwroot\\bootstrap\\scss\\bootstrap-utilities.scss", "SourceId": "AspCoreMVC6", "SourceType": "Discovered", "ContentRoot": "C:\\Users\\<USER>\\source\\repos\\Almutasim90\\AspCoreMVC6\\AspCoreMVC6\\wwwroot\\", "BasePath": "_content/AspCoreMVC6", "RelativePath": "bootstrap/scss/bootstrap-utilities.scss", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "RelatedAsset": "", "AssetTraitName": "", "AssetTraitValue": "", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\bootstrap\\scss\\bootstrap-utilities.scss"}, {"Identity": "C:\\Users\\<USER>\\source\\repos\\Almutasim90\\AspCoreMVC6\\AspCoreMVC6\\wwwroot\\bootstrap\\scss\\forms\\_floating-labels.scss", "SourceId": "AspCoreMVC6", "SourceType": "Discovered", "ContentRoot": "C:\\Users\\<USER>\\source\\repos\\Almutasim90\\AspCoreMVC6\\AspCoreMVC6\\wwwroot\\", "BasePath": "_content/AspCoreMVC6", "RelativePath": "bootstrap/scss/forms/_floating-labels.scss", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "RelatedAsset": "", "AssetTraitName": "", "AssetTraitValue": "", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\bootstrap\\scss\\forms\\_floating-labels.scss"}, {"Identity": "C:\\Users\\<USER>\\source\\repos\\Almutasim90\\AspCoreMVC6\\AspCoreMVC6\\wwwroot\\bootstrap\\scss\\forms\\_form-check.scss", "SourceId": "AspCoreMVC6", "SourceType": "Discovered", "ContentRoot": "C:\\Users\\<USER>\\source\\repos\\Almutasim90\\AspCoreMVC6\\AspCoreMVC6\\wwwroot\\", "BasePath": "_content/AspCoreMVC6", "RelativePath": "bootstrap/scss/forms/_form-check.scss", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "RelatedAsset": "", "AssetTraitName": "", "AssetTraitValue": "", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\bootstrap\\scss\\forms\\_form-check.scss"}, {"Identity": "C:\\Users\\<USER>\\source\\repos\\Almutasim90\\AspCoreMVC6\\AspCoreMVC6\\wwwroot\\bootstrap\\scss\\forms\\_form-control.scss", "SourceId": "AspCoreMVC6", "SourceType": "Discovered", "ContentRoot": "C:\\Users\\<USER>\\source\\repos\\Almutasim90\\AspCoreMVC6\\AspCoreMVC6\\wwwroot\\", "BasePath": "_content/AspCoreMVC6", "RelativePath": "bootstrap/scss/forms/_form-control.scss", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "RelatedAsset": "", "AssetTraitName": "", "AssetTraitValue": "", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\bootstrap\\scss\\forms\\_form-control.scss"}, {"Identity": "C:\\Users\\<USER>\\source\\repos\\Almutasim90\\AspCoreMVC6\\AspCoreMVC6\\wwwroot\\bootstrap\\scss\\forms\\_form-range.scss", "SourceId": "AspCoreMVC6", "SourceType": "Discovered", "ContentRoot": "C:\\Users\\<USER>\\source\\repos\\Almutasim90\\AspCoreMVC6\\AspCoreMVC6\\wwwroot\\", "BasePath": "_content/AspCoreMVC6", "RelativePath": "bootstrap/scss/forms/_form-range.scss", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "RelatedAsset": "", "AssetTraitName": "", "AssetTraitValue": "", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\bootstrap\\scss\\forms\\_form-range.scss"}, {"Identity": "C:\\Users\\<USER>\\source\\repos\\Almutasim90\\AspCoreMVC6\\AspCoreMVC6\\wwwroot\\bootstrap\\scss\\forms\\_form-select.scss", "SourceId": "AspCoreMVC6", "SourceType": "Discovered", "ContentRoot": "C:\\Users\\<USER>\\source\\repos\\Almutasim90\\AspCoreMVC6\\AspCoreMVC6\\wwwroot\\", "BasePath": "_content/AspCoreMVC6", "RelativePath": "bootstrap/scss/forms/_form-select.scss", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "RelatedAsset": "", "AssetTraitName": "", "AssetTraitValue": "", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\bootstrap\\scss\\forms\\_form-select.scss"}, {"Identity": "C:\\Users\\<USER>\\source\\repos\\Almutasim90\\AspCoreMVC6\\AspCoreMVC6\\wwwroot\\bootstrap\\scss\\forms\\_form-text.scss", "SourceId": "AspCoreMVC6", "SourceType": "Discovered", "ContentRoot": "C:\\Users\\<USER>\\source\\repos\\Almutasim90\\AspCoreMVC6\\AspCoreMVC6\\wwwroot\\", "BasePath": "_content/AspCoreMVC6", "RelativePath": "bootstrap/scss/forms/_form-text.scss", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "RelatedAsset": "", "AssetTraitName": "", "AssetTraitValue": "", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\bootstrap\\scss\\forms\\_form-text.scss"}, {"Identity": "C:\\Users\\<USER>\\source\\repos\\Almutasim90\\AspCoreMVC6\\AspCoreMVC6\\wwwroot\\bootstrap\\scss\\forms\\_input-group.scss", "SourceId": "AspCoreMVC6", "SourceType": "Discovered", "ContentRoot": "C:\\Users\\<USER>\\source\\repos\\Almutasim90\\AspCoreMVC6\\AspCoreMVC6\\wwwroot\\", "BasePath": "_content/AspCoreMVC6", "RelativePath": "bootstrap/scss/forms/_input-group.scss", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "RelatedAsset": "", "AssetTraitName": "", "AssetTraitValue": "", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\bootstrap\\scss\\forms\\_input-group.scss"}, {"Identity": "C:\\Users\\<USER>\\source\\repos\\Almutasim90\\AspCoreMVC6\\AspCoreMVC6\\wwwroot\\bootstrap\\scss\\forms\\_labels.scss", "SourceId": "AspCoreMVC6", "SourceType": "Discovered", "ContentRoot": "C:\\Users\\<USER>\\source\\repos\\Almutasim90\\AspCoreMVC6\\AspCoreMVC6\\wwwroot\\", "BasePath": "_content/AspCoreMVC6", "RelativePath": "bootstrap/scss/forms/_labels.scss", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "RelatedAsset": "", "AssetTraitName": "", "AssetTraitValue": "", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\bootstrap\\scss\\forms\\_labels.scss"}, {"Identity": "C:\\Users\\<USER>\\source\\repos\\Almutasim90\\AspCoreMVC6\\AspCoreMVC6\\wwwroot\\bootstrap\\scss\\forms\\_validation.scss", "SourceId": "AspCoreMVC6", "SourceType": "Discovered", "ContentRoot": "C:\\Users\\<USER>\\source\\repos\\Almutasim90\\AspCoreMVC6\\AspCoreMVC6\\wwwroot\\", "BasePath": "_content/AspCoreMVC6", "RelativePath": "bootstrap/scss/forms/_validation.scss", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "RelatedAsset": "", "AssetTraitName": "", "AssetTraitValue": "", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\bootstrap\\scss\\forms\\_validation.scss"}, {"Identity": "C:\\Users\\<USER>\\source\\repos\\Almutasim90\\AspCoreMVC6\\AspCoreMVC6\\wwwroot\\bootstrap\\scss\\helpers\\_clearfix.scss", "SourceId": "AspCoreMVC6", "SourceType": "Discovered", "ContentRoot": "C:\\Users\\<USER>\\source\\repos\\Almutasim90\\AspCoreMVC6\\AspCoreMVC6\\wwwroot\\", "BasePath": "_content/AspCoreMVC6", "RelativePath": "bootstrap/scss/helpers/_clearfix.scss", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "RelatedAsset": "", "AssetTraitName": "", "AssetTraitValue": "", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\bootstrap\\scss\\helpers\\_clearfix.scss"}, {"Identity": "C:\\Users\\<USER>\\source\\repos\\Almutasim90\\AspCoreMVC6\\AspCoreMVC6\\wwwroot\\bootstrap\\scss\\helpers\\_color-bg.scss", "SourceId": "AspCoreMVC6", "SourceType": "Discovered", "ContentRoot": "C:\\Users\\<USER>\\source\\repos\\Almutasim90\\AspCoreMVC6\\AspCoreMVC6\\wwwroot\\", "BasePath": "_content/AspCoreMVC6", "RelativePath": "bootstrap/scss/helpers/_color-bg.scss", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "RelatedAsset": "", "AssetTraitName": "", "AssetTraitValue": "", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\bootstrap\\scss\\helpers\\_color-bg.scss"}, {"Identity": "C:\\Users\\<USER>\\source\\repos\\Almutasim90\\AspCoreMVC6\\AspCoreMVC6\\wwwroot\\bootstrap\\scss\\helpers\\_colored-links.scss", "SourceId": "AspCoreMVC6", "SourceType": "Discovered", "ContentRoot": "C:\\Users\\<USER>\\source\\repos\\Almutasim90\\AspCoreMVC6\\AspCoreMVC6\\wwwroot\\", "BasePath": "_content/AspCoreMVC6", "RelativePath": "bootstrap/scss/helpers/_colored-links.scss", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "RelatedAsset": "", "AssetTraitName": "", "AssetTraitValue": "", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\bootstrap\\scss\\helpers\\_colored-links.scss"}, {"Identity": "C:\\Users\\<USER>\\source\\repos\\Almutasim90\\AspCoreMVC6\\AspCoreMVC6\\wwwroot\\bootstrap\\scss\\helpers\\_position.scss", "SourceId": "AspCoreMVC6", "SourceType": "Discovered", "ContentRoot": "C:\\Users\\<USER>\\source\\repos\\Almutasim90\\AspCoreMVC6\\AspCoreMVC6\\wwwroot\\", "BasePath": "_content/AspCoreMVC6", "RelativePath": "bootstrap/scss/helpers/_position.scss", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "RelatedAsset": "", "AssetTraitName": "", "AssetTraitValue": "", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\bootstrap\\scss\\helpers\\_position.scss"}, {"Identity": "C:\\Users\\<USER>\\source\\repos\\Almutasim90\\AspCoreMVC6\\AspCoreMVC6\\wwwroot\\bootstrap\\scss\\helpers\\_ratio.scss", "SourceId": "AspCoreMVC6", "SourceType": "Discovered", "ContentRoot": "C:\\Users\\<USER>\\source\\repos\\Almutasim90\\AspCoreMVC6\\AspCoreMVC6\\wwwroot\\", "BasePath": "_content/AspCoreMVC6", "RelativePath": "bootstrap/scss/helpers/_ratio.scss", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "RelatedAsset": "", "AssetTraitName": "", "AssetTraitValue": "", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\bootstrap\\scss\\helpers\\_ratio.scss"}, {"Identity": "C:\\Users\\<USER>\\source\\repos\\Almutasim90\\AspCoreMVC6\\AspCoreMVC6\\wwwroot\\bootstrap\\scss\\helpers\\_stacks.scss", "SourceId": "AspCoreMVC6", "SourceType": "Discovered", "ContentRoot": "C:\\Users\\<USER>\\source\\repos\\Almutasim90\\AspCoreMVC6\\AspCoreMVC6\\wwwroot\\", "BasePath": "_content/AspCoreMVC6", "RelativePath": "bootstrap/scss/helpers/_stacks.scss", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "RelatedAsset": "", "AssetTraitName": "", "AssetTraitValue": "", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\bootstrap\\scss\\helpers\\_stacks.scss"}, {"Identity": "C:\\Users\\<USER>\\source\\repos\\Almutasim90\\AspCoreMVC6\\AspCoreMVC6\\wwwroot\\bootstrap\\scss\\helpers\\_stretched-link.scss", "SourceId": "AspCoreMVC6", "SourceType": "Discovered", "ContentRoot": "C:\\Users\\<USER>\\source\\repos\\Almutasim90\\AspCoreMVC6\\AspCoreMVC6\\wwwroot\\", "BasePath": "_content/AspCoreMVC6", "RelativePath": "bootstrap/scss/helpers/_stretched-link.scss", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "RelatedAsset": "", "AssetTraitName": "", "AssetTraitValue": "", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\bootstrap\\scss\\helpers\\_stretched-link.scss"}, {"Identity": "C:\\Users\\<USER>\\source\\repos\\Almutasim90\\AspCoreMVC6\\AspCoreMVC6\\wwwroot\\bootstrap\\scss\\helpers\\_text-truncation.scss", "SourceId": "AspCoreMVC6", "SourceType": "Discovered", "ContentRoot": "C:\\Users\\<USER>\\source\\repos\\Almutasim90\\AspCoreMVC6\\AspCoreMVC6\\wwwroot\\", "BasePath": "_content/AspCoreMVC6", "RelativePath": "bootstrap/scss/helpers/_text-truncation.scss", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "RelatedAsset": "", "AssetTraitName": "", "AssetTraitValue": "", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\bootstrap\\scss\\helpers\\_text-truncation.scss"}, {"Identity": "C:\\Users\\<USER>\\source\\repos\\Almutasim90\\AspCoreMVC6\\AspCoreMVC6\\wwwroot\\bootstrap\\scss\\helpers\\_visually-hidden.scss", "SourceId": "AspCoreMVC6", "SourceType": "Discovered", "ContentRoot": "C:\\Users\\<USER>\\source\\repos\\Almutasim90\\AspCoreMVC6\\AspCoreMVC6\\wwwroot\\", "BasePath": "_content/AspCoreMVC6", "RelativePath": "bootstrap/scss/helpers/_visually-hidden.scss", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "RelatedAsset": "", "AssetTraitName": "", "AssetTraitValue": "", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\bootstrap\\scss\\helpers\\_visually-hidden.scss"}, {"Identity": "C:\\Users\\<USER>\\source\\repos\\Almutasim90\\AspCoreMVC6\\AspCoreMVC6\\wwwroot\\bootstrap\\scss\\helpers\\_vr.scss", "SourceId": "AspCoreMVC6", "SourceType": "Discovered", "ContentRoot": "C:\\Users\\<USER>\\source\\repos\\Almutasim90\\AspCoreMVC6\\AspCoreMVC6\\wwwroot\\", "BasePath": "_content/AspCoreMVC6", "RelativePath": "bootstrap/scss/helpers/_vr.scss", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "RelatedAsset": "", "AssetTraitName": "", "AssetTraitValue": "", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\bootstrap\\scss\\helpers\\_vr.scss"}, {"Identity": "C:\\Users\\<USER>\\source\\repos\\Almutasim90\\AspCoreMVC6\\AspCoreMVC6\\wwwroot\\bootstrap\\scss\\mixins\\_alert.scss", "SourceId": "AspCoreMVC6", "SourceType": "Discovered", "ContentRoot": "C:\\Users\\<USER>\\source\\repos\\Almutasim90\\AspCoreMVC6\\AspCoreMVC6\\wwwroot\\", "BasePath": "_content/AspCoreMVC6", "RelativePath": "bootstrap/scss/mixins/_alert.scss", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "RelatedAsset": "", "AssetTraitName": "", "AssetTraitValue": "", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\bootstrap\\scss\\mixins\\_alert.scss"}, {"Identity": "C:\\Users\\<USER>\\source\\repos\\Almutasim90\\AspCoreMVC6\\AspCoreMVC6\\wwwroot\\bootstrap\\scss\\mixins\\_backdrop.scss", "SourceId": "AspCoreMVC6", "SourceType": "Discovered", "ContentRoot": "C:\\Users\\<USER>\\source\\repos\\Almutasim90\\AspCoreMVC6\\AspCoreMVC6\\wwwroot\\", "BasePath": "_content/AspCoreMVC6", "RelativePath": "bootstrap/scss/mixins/_backdrop.scss", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "RelatedAsset": "", "AssetTraitName": "", "AssetTraitValue": "", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\bootstrap\\scss\\mixins\\_backdrop.scss"}, {"Identity": "C:\\Users\\<USER>\\source\\repos\\Almutasim90\\AspCoreMVC6\\AspCoreMVC6\\wwwroot\\bootstrap\\scss\\mixins\\_banner.scss", "SourceId": "AspCoreMVC6", "SourceType": "Discovered", "ContentRoot": "C:\\Users\\<USER>\\source\\repos\\Almutasim90\\AspCoreMVC6\\AspCoreMVC6\\wwwroot\\", "BasePath": "_content/AspCoreMVC6", "RelativePath": "bootstrap/scss/mixins/_banner.scss", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "RelatedAsset": "", "AssetTraitName": "", "AssetTraitValue": "", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\bootstrap\\scss\\mixins\\_banner.scss"}, {"Identity": "C:\\Users\\<USER>\\source\\repos\\Almutasim90\\AspCoreMVC6\\AspCoreMVC6\\wwwroot\\bootstrap\\scss\\mixins\\_border-radius.scss", "SourceId": "AspCoreMVC6", "SourceType": "Discovered", "ContentRoot": "C:\\Users\\<USER>\\source\\repos\\Almutasim90\\AspCoreMVC6\\AspCoreMVC6\\wwwroot\\", "BasePath": "_content/AspCoreMVC6", "RelativePath": "bootstrap/scss/mixins/_border-radius.scss", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "RelatedAsset": "", "AssetTraitName": "", "AssetTraitValue": "", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\bootstrap\\scss\\mixins\\_border-radius.scss"}, {"Identity": "C:\\Users\\<USER>\\source\\repos\\Almutasim90\\AspCoreMVC6\\AspCoreMVC6\\wwwroot\\bootstrap\\scss\\mixins\\_box-shadow.scss", "SourceId": "AspCoreMVC6", "SourceType": "Discovered", "ContentRoot": "C:\\Users\\<USER>\\source\\repos\\Almutasim90\\AspCoreMVC6\\AspCoreMVC6\\wwwroot\\", "BasePath": "_content/AspCoreMVC6", "RelativePath": "bootstrap/scss/mixins/_box-shadow.scss", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "RelatedAsset": "", "AssetTraitName": "", "AssetTraitValue": "", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\bootstrap\\scss\\mixins\\_box-shadow.scss"}, {"Identity": "C:\\Users\\<USER>\\source\\repos\\Almutasim90\\AspCoreMVC6\\AspCoreMVC6\\wwwroot\\bootstrap\\scss\\mixins\\_breakpoints.scss", "SourceId": "AspCoreMVC6", "SourceType": "Discovered", "ContentRoot": "C:\\Users\\<USER>\\source\\repos\\Almutasim90\\AspCoreMVC6\\AspCoreMVC6\\wwwroot\\", "BasePath": "_content/AspCoreMVC6", "RelativePath": "bootstrap/scss/mixins/_breakpoints.scss", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "RelatedAsset": "", "AssetTraitName": "", "AssetTraitValue": "", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\bootstrap\\scss\\mixins\\_breakpoints.scss"}, {"Identity": "C:\\Users\\<USER>\\source\\repos\\Almutasim90\\AspCoreMVC6\\AspCoreMVC6\\wwwroot\\bootstrap\\scss\\mixins\\_buttons.scss", "SourceId": "AspCoreMVC6", "SourceType": "Discovered", "ContentRoot": "C:\\Users\\<USER>\\source\\repos\\Almutasim90\\AspCoreMVC6\\AspCoreMVC6\\wwwroot\\", "BasePath": "_content/AspCoreMVC6", "RelativePath": "bootstrap/scss/mixins/_buttons.scss", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "RelatedAsset": "", "AssetTraitName": "", "AssetTraitValue": "", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\bootstrap\\scss\\mixins\\_buttons.scss"}, {"Identity": "C:\\Users\\<USER>\\source\\repos\\Almutasim90\\AspCoreMVC6\\AspCoreMVC6\\wwwroot\\bootstrap\\scss\\mixins\\_caret.scss", "SourceId": "AspCoreMVC6", "SourceType": "Discovered", "ContentRoot": "C:\\Users\\<USER>\\source\\repos\\Almutasim90\\AspCoreMVC6\\AspCoreMVC6\\wwwroot\\", "BasePath": "_content/AspCoreMVC6", "RelativePath": "bootstrap/scss/mixins/_caret.scss", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "RelatedAsset": "", "AssetTraitName": "", "AssetTraitValue": "", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\bootstrap\\scss\\mixins\\_caret.scss"}, {"Identity": "C:\\Users\\<USER>\\source\\repos\\Almutasim90\\AspCoreMVC6\\AspCoreMVC6\\wwwroot\\bootstrap\\scss\\mixins\\_clearfix.scss", "SourceId": "AspCoreMVC6", "SourceType": "Discovered", "ContentRoot": "C:\\Users\\<USER>\\source\\repos\\Almutasim90\\AspCoreMVC6\\AspCoreMVC6\\wwwroot\\", "BasePath": "_content/AspCoreMVC6", "RelativePath": "bootstrap/scss/mixins/_clearfix.scss", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "RelatedAsset": "", "AssetTraitName": "", "AssetTraitValue": "", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\bootstrap\\scss\\mixins\\_clearfix.scss"}, {"Identity": "C:\\Users\\<USER>\\source\\repos\\Almutasim90\\AspCoreMVC6\\AspCoreMVC6\\wwwroot\\bootstrap\\scss\\mixins\\_color-scheme.scss", "SourceId": "AspCoreMVC6", "SourceType": "Discovered", "ContentRoot": "C:\\Users\\<USER>\\source\\repos\\Almutasim90\\AspCoreMVC6\\AspCoreMVC6\\wwwroot\\", "BasePath": "_content/AspCoreMVC6", "RelativePath": "bootstrap/scss/mixins/_color-scheme.scss", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "RelatedAsset": "", "AssetTraitName": "", "AssetTraitValue": "", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\bootstrap\\scss\\mixins\\_color-scheme.scss"}, {"Identity": "C:\\Users\\<USER>\\source\\repos\\Almutasim90\\AspCoreMVC6\\AspCoreMVC6\\wwwroot\\bootstrap\\scss\\mixins\\_container.scss", "SourceId": "AspCoreMVC6", "SourceType": "Discovered", "ContentRoot": "C:\\Users\\<USER>\\source\\repos\\Almutasim90\\AspCoreMVC6\\AspCoreMVC6\\wwwroot\\", "BasePath": "_content/AspCoreMVC6", "RelativePath": "bootstrap/scss/mixins/_container.scss", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "RelatedAsset": "", "AssetTraitName": "", "AssetTraitValue": "", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\bootstrap\\scss\\mixins\\_container.scss"}, {"Identity": "C:\\Users\\<USER>\\source\\repos\\Almutasim90\\AspCoreMVC6\\AspCoreMVC6\\wwwroot\\bootstrap\\scss\\mixins\\_deprecate.scss", "SourceId": "AspCoreMVC6", "SourceType": "Discovered", "ContentRoot": "C:\\Users\\<USER>\\source\\repos\\Almutasim90\\AspCoreMVC6\\AspCoreMVC6\\wwwroot\\", "BasePath": "_content/AspCoreMVC6", "RelativePath": "bootstrap/scss/mixins/_deprecate.scss", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "RelatedAsset": "", "AssetTraitName": "", "AssetTraitValue": "", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\bootstrap\\scss\\mixins\\_deprecate.scss"}, {"Identity": "C:\\Users\\<USER>\\source\\repos\\Almutasim90\\AspCoreMVC6\\AspCoreMVC6\\wwwroot\\bootstrap\\scss\\mixins\\_forms.scss", "SourceId": "AspCoreMVC6", "SourceType": "Discovered", "ContentRoot": "C:\\Users\\<USER>\\source\\repos\\Almutasim90\\AspCoreMVC6\\AspCoreMVC6\\wwwroot\\", "BasePath": "_content/AspCoreMVC6", "RelativePath": "bootstrap/scss/mixins/_forms.scss", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "RelatedAsset": "", "AssetTraitName": "", "AssetTraitValue": "", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\bootstrap\\scss\\mixins\\_forms.scss"}, {"Identity": "C:\\Users\\<USER>\\source\\repos\\Almutasim90\\AspCoreMVC6\\AspCoreMVC6\\wwwroot\\bootstrap\\scss\\mixins\\_gradients.scss", "SourceId": "AspCoreMVC6", "SourceType": "Discovered", "ContentRoot": "C:\\Users\\<USER>\\source\\repos\\Almutasim90\\AspCoreMVC6\\AspCoreMVC6\\wwwroot\\", "BasePath": "_content/AspCoreMVC6", "RelativePath": "bootstrap/scss/mixins/_gradients.scss", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "RelatedAsset": "", "AssetTraitName": "", "AssetTraitValue": "", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\bootstrap\\scss\\mixins\\_gradients.scss"}, {"Identity": "C:\\Users\\<USER>\\source\\repos\\Almutasim90\\AspCoreMVC6\\AspCoreMVC6\\wwwroot\\bootstrap\\scss\\mixins\\_grid.scss", "SourceId": "AspCoreMVC6", "SourceType": "Discovered", "ContentRoot": "C:\\Users\\<USER>\\source\\repos\\Almutasim90\\AspCoreMVC6\\AspCoreMVC6\\wwwroot\\", "BasePath": "_content/AspCoreMVC6", "RelativePath": "bootstrap/scss/mixins/_grid.scss", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "RelatedAsset": "", "AssetTraitName": "", "AssetTraitValue": "", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\bootstrap\\scss\\mixins\\_grid.scss"}, {"Identity": "C:\\Users\\<USER>\\source\\repos\\Almutasim90\\AspCoreMVC6\\AspCoreMVC6\\wwwroot\\bootstrap\\scss\\mixins\\_image.scss", "SourceId": "AspCoreMVC6", "SourceType": "Discovered", "ContentRoot": "C:\\Users\\<USER>\\source\\repos\\Almutasim90\\AspCoreMVC6\\AspCoreMVC6\\wwwroot\\", "BasePath": "_content/AspCoreMVC6", "RelativePath": "bootstrap/scss/mixins/_image.scss", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "RelatedAsset": "", "AssetTraitName": "", "AssetTraitValue": "", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\bootstrap\\scss\\mixins\\_image.scss"}, {"Identity": "C:\\Users\\<USER>\\source\\repos\\Almutasim90\\AspCoreMVC6\\AspCoreMVC6\\wwwroot\\bootstrap\\scss\\mixins\\_list-group.scss", "SourceId": "AspCoreMVC6", "SourceType": "Discovered", "ContentRoot": "C:\\Users\\<USER>\\source\\repos\\Almutasim90\\AspCoreMVC6\\AspCoreMVC6\\wwwroot\\", "BasePath": "_content/AspCoreMVC6", "RelativePath": "bootstrap/scss/mixins/_list-group.scss", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "RelatedAsset": "", "AssetTraitName": "", "AssetTraitValue": "", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\bootstrap\\scss\\mixins\\_list-group.scss"}, {"Identity": "C:\\Users\\<USER>\\source\\repos\\Almutasim90\\AspCoreMVC6\\AspCoreMVC6\\wwwroot\\bootstrap\\scss\\mixins\\_lists.scss", "SourceId": "AspCoreMVC6", "SourceType": "Discovered", "ContentRoot": "C:\\Users\\<USER>\\source\\repos\\Almutasim90\\AspCoreMVC6\\AspCoreMVC6\\wwwroot\\", "BasePath": "_content/AspCoreMVC6", "RelativePath": "bootstrap/scss/mixins/_lists.scss", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "RelatedAsset": "", "AssetTraitName": "", "AssetTraitValue": "", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\bootstrap\\scss\\mixins\\_lists.scss"}, {"Identity": "C:\\Users\\<USER>\\source\\repos\\Almutasim90\\AspCoreMVC6\\AspCoreMVC6\\wwwroot\\bootstrap\\scss\\mixins\\_pagination.scss", "SourceId": "AspCoreMVC6", "SourceType": "Discovered", "ContentRoot": "C:\\Users\\<USER>\\source\\repos\\Almutasim90\\AspCoreMVC6\\AspCoreMVC6\\wwwroot\\", "BasePath": "_content/AspCoreMVC6", "RelativePath": "bootstrap/scss/mixins/_pagination.scss", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "RelatedAsset": "", "AssetTraitName": "", "AssetTraitValue": "", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\bootstrap\\scss\\mixins\\_pagination.scss"}, {"Identity": "C:\\Users\\<USER>\\source\\repos\\Almutasim90\\AspCoreMVC6\\AspCoreMVC6\\wwwroot\\bootstrap\\scss\\mixins\\_reset-text.scss", "SourceId": "AspCoreMVC6", "SourceType": "Discovered", "ContentRoot": "C:\\Users\\<USER>\\source\\repos\\Almutasim90\\AspCoreMVC6\\AspCoreMVC6\\wwwroot\\", "BasePath": "_content/AspCoreMVC6", "RelativePath": "bootstrap/scss/mixins/_reset-text.scss", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "RelatedAsset": "", "AssetTraitName": "", "AssetTraitValue": "", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\bootstrap\\scss\\mixins\\_reset-text.scss"}, {"Identity": "C:\\Users\\<USER>\\source\\repos\\Almutasim90\\AspCoreMVC6\\AspCoreMVC6\\wwwroot\\bootstrap\\scss\\mixins\\_resize.scss", "SourceId": "AspCoreMVC6", "SourceType": "Discovered", "ContentRoot": "C:\\Users\\<USER>\\source\\repos\\Almutasim90\\AspCoreMVC6\\AspCoreMVC6\\wwwroot\\", "BasePath": "_content/AspCoreMVC6", "RelativePath": "bootstrap/scss/mixins/_resize.scss", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "RelatedAsset": "", "AssetTraitName": "", "AssetTraitValue": "", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\bootstrap\\scss\\mixins\\_resize.scss"}, {"Identity": "C:\\Users\\<USER>\\source\\repos\\Almutasim90\\AspCoreMVC6\\AspCoreMVC6\\wwwroot\\bootstrap\\scss\\mixins\\_table-variants.scss", "SourceId": "AspCoreMVC6", "SourceType": "Discovered", "ContentRoot": "C:\\Users\\<USER>\\source\\repos\\Almutasim90\\AspCoreMVC6\\AspCoreMVC6\\wwwroot\\", "BasePath": "_content/AspCoreMVC6", "RelativePath": "bootstrap/scss/mixins/_table-variants.scss", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "RelatedAsset": "", "AssetTraitName": "", "AssetTraitValue": "", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\bootstrap\\scss\\mixins\\_table-variants.scss"}, {"Identity": "C:\\Users\\<USER>\\source\\repos\\Almutasim90\\AspCoreMVC6\\AspCoreMVC6\\wwwroot\\bootstrap\\scss\\mixins\\_text-truncate.scss", "SourceId": "AspCoreMVC6", "SourceType": "Discovered", "ContentRoot": "C:\\Users\\<USER>\\source\\repos\\Almutasim90\\AspCoreMVC6\\AspCoreMVC6\\wwwroot\\", "BasePath": "_content/AspCoreMVC6", "RelativePath": "bootstrap/scss/mixins/_text-truncate.scss", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "RelatedAsset": "", "AssetTraitName": "", "AssetTraitValue": "", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\bootstrap\\scss\\mixins\\_text-truncate.scss"}, {"Identity": "C:\\Users\\<USER>\\source\\repos\\Almutasim90\\AspCoreMVC6\\AspCoreMVC6\\wwwroot\\bootstrap\\scss\\mixins\\_transition.scss", "SourceId": "AspCoreMVC6", "SourceType": "Discovered", "ContentRoot": "C:\\Users\\<USER>\\source\\repos\\Almutasim90\\AspCoreMVC6\\AspCoreMVC6\\wwwroot\\", "BasePath": "_content/AspCoreMVC6", "RelativePath": "bootstrap/scss/mixins/_transition.scss", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "RelatedAsset": "", "AssetTraitName": "", "AssetTraitValue": "", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\bootstrap\\scss\\mixins\\_transition.scss"}, {"Identity": "C:\\Users\\<USER>\\source\\repos\\Almutasim90\\AspCoreMVC6\\AspCoreMVC6\\wwwroot\\bootstrap\\scss\\mixins\\_utilities.scss", "SourceId": "AspCoreMVC6", "SourceType": "Discovered", "ContentRoot": "C:\\Users\\<USER>\\source\\repos\\Almutasim90\\AspCoreMVC6\\AspCoreMVC6\\wwwroot\\", "BasePath": "_content/AspCoreMVC6", "RelativePath": "bootstrap/scss/mixins/_utilities.scss", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "RelatedAsset": "", "AssetTraitName": "", "AssetTraitValue": "", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\bootstrap\\scss\\mixins\\_utilities.scss"}, {"Identity": "C:\\Users\\<USER>\\source\\repos\\Almutasim90\\AspCoreMVC6\\AspCoreMVC6\\wwwroot\\bootstrap\\scss\\mixins\\_visually-hidden.scss", "SourceId": "AspCoreMVC6", "SourceType": "Discovered", "ContentRoot": "C:\\Users\\<USER>\\source\\repos\\Almutasim90\\AspCoreMVC6\\AspCoreMVC6\\wwwroot\\", "BasePath": "_content/AspCoreMVC6", "RelativePath": "bootstrap/scss/mixins/_visually-hidden.scss", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "RelatedAsset": "", "AssetTraitName": "", "AssetTraitValue": "", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\bootstrap\\scss\\mixins\\_visually-hidden.scss"}, {"Identity": "C:\\Users\\<USER>\\source\\repos\\Almutasim90\\AspCoreMVC6\\AspCoreMVC6\\wwwroot\\bootstrap\\scss\\utilities\\_api.scss", "SourceId": "AspCoreMVC6", "SourceType": "Discovered", "ContentRoot": "C:\\Users\\<USER>\\source\\repos\\Almutasim90\\AspCoreMVC6\\AspCoreMVC6\\wwwroot\\", "BasePath": "_content/AspCoreMVC6", "RelativePath": "bootstrap/scss/utilities/_api.scss", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "RelatedAsset": "", "AssetTraitName": "", "AssetTraitValue": "", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\bootstrap\\scss\\utilities\\_api.scss"}, {"Identity": "C:\\Users\\<USER>\\source\\repos\\Almutasim90\\AspCoreMVC6\\AspCoreMVC6\\wwwroot\\bootstrap\\scss\\vendor\\_rfs.scss", "SourceId": "AspCoreMVC6", "SourceType": "Discovered", "ContentRoot": "C:\\Users\\<USER>\\source\\repos\\Almutasim90\\AspCoreMVC6\\AspCoreMVC6\\wwwroot\\", "BasePath": "_content/AspCoreMVC6", "RelativePath": "bootstrap/scss/vendor/_rfs.scss", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "RelatedAsset": "", "AssetTraitName": "", "AssetTraitValue": "", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\bootstrap\\scss\\vendor\\_rfs.scss"}, {"Identity": "C:\\Users\\<USER>\\source\\repos\\Almutasim90\\AspCoreMVC6\\AspCoreMVC6\\wwwroot\\css\\bootswatch.css", "SourceId": "AspCoreMVC6", "SourceType": "Discovered", "ContentRoot": "C:\\Users\\<USER>\\source\\repos\\Almutasim90\\AspCoreMVC6\\AspCoreMVC6\\wwwroot\\", "BasePath": "_content/AspCoreMVC6", "RelativePath": "css/bootswatch.css", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "RelatedAsset": "", "AssetTraitName": "", "AssetTraitValue": "", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\css\\bootswatch.css"}, {"Identity": "C:\\Users\\<USER>\\source\\repos\\Almutasim90\\AspCoreMVC6\\AspCoreMVC6\\wwwroot\\css\\site.css", "SourceId": "AspCoreMVC6", "SourceType": "Discovered", "ContentRoot": "C:\\Users\\<USER>\\source\\repos\\Almutasim90\\AspCoreMVC6\\AspCoreMVC6\\wwwroot\\", "BasePath": "_content/AspCoreMVC6", "RelativePath": "css/site.css", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "RelatedAsset": "", "AssetTraitName": "", "AssetTraitValue": "", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\css\\site.css"}, {"Identity": "C:\\Users\\<USER>\\source\\repos\\Almutasim90\\AspCoreMVC6\\AspCoreMVC6\\wwwroot\\favicon.ico", "SourceId": "AspCoreMVC6", "SourceType": "Discovered", "ContentRoot": "C:\\Users\\<USER>\\source\\repos\\Almutasim90\\AspCoreMVC6\\AspCoreMVC6\\wwwroot\\", "BasePath": "_content/AspCoreMVC6", "RelativePath": "favicon.ico", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "RelatedAsset": "", "AssetTraitName": "", "AssetTraitValue": "", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\favicon.ico"}, {"Identity": "C:\\Users\\<USER>\\source\\repos\\Almutasim90\\AspCoreMVC6\\AspCoreMVC6\\wwwroot\\fonts\\Tajawal-Black.ttf", "SourceId": "AspCoreMVC6", "SourceType": "Discovered", "ContentRoot": "C:\\Users\\<USER>\\source\\repos\\Almutasim90\\AspCoreMVC6\\AspCoreMVC6\\wwwroot\\", "BasePath": "_content/AspCoreMVC6", "RelativePath": "fonts/Tajawal-Black.ttf", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "RelatedAsset": "", "AssetTraitName": "", "AssetTraitValue": "", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\fonts\\Tajawal-Black.ttf"}, {"Identity": "C:\\Users\\<USER>\\source\\repos\\Almutasim90\\AspCoreMVC6\\AspCoreMVC6\\wwwroot\\fonts\\Tajawal-Bold.ttf", "SourceId": "AspCoreMVC6", "SourceType": "Discovered", "ContentRoot": "C:\\Users\\<USER>\\source\\repos\\Almutasim90\\AspCoreMVC6\\AspCoreMVC6\\wwwroot\\", "BasePath": "_content/AspCoreMVC6", "RelativePath": "fonts/Tajawal-Bold.ttf", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "RelatedAsset": "", "AssetTraitName": "", "AssetTraitValue": "", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\fonts\\Tajawal-Bold.ttf"}, {"Identity": "C:\\Users\\<USER>\\source\\repos\\Almutasim90\\AspCoreMVC6\\AspCoreMVC6\\wwwroot\\fonts\\Tajawal-ExtraBold.ttf", "SourceId": "AspCoreMVC6", "SourceType": "Discovered", "ContentRoot": "C:\\Users\\<USER>\\source\\repos\\Almutasim90\\AspCoreMVC6\\AspCoreMVC6\\wwwroot\\", "BasePath": "_content/AspCoreMVC6", "RelativePath": "fonts/Tajawal-ExtraBold.ttf", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "RelatedAsset": "", "AssetTraitName": "", "AssetTraitValue": "", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\fonts\\Tajawal-ExtraBold.ttf"}, {"Identity": "C:\\Users\\<USER>\\source\\repos\\Almutasim90\\AspCoreMVC6\\AspCoreMVC6\\wwwroot\\fonts\\Tajawal-ExtraLight.ttf", "SourceId": "AspCoreMVC6", "SourceType": "Discovered", "ContentRoot": "C:\\Users\\<USER>\\source\\repos\\Almutasim90\\AspCoreMVC6\\AspCoreMVC6\\wwwroot\\", "BasePath": "_content/AspCoreMVC6", "RelativePath": "fonts/Tajawal-ExtraLight.ttf", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "RelatedAsset": "", "AssetTraitName": "", "AssetTraitValue": "", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\fonts\\Tajawal-ExtraLight.ttf"}, {"Identity": "C:\\Users\\<USER>\\source\\repos\\Almutasim90\\AspCoreMVC6\\AspCoreMVC6\\wwwroot\\fonts\\Tajawal-Light.ttf", "SourceId": "AspCoreMVC6", "SourceType": "Discovered", "ContentRoot": "C:\\Users\\<USER>\\source\\repos\\Almutasim90\\AspCoreMVC6\\AspCoreMVC6\\wwwroot\\", "BasePath": "_content/AspCoreMVC6", "RelativePath": "fonts/Tajawal-Light.ttf", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "RelatedAsset": "", "AssetTraitName": "", "AssetTraitValue": "", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\fonts\\Tajawal-Light.ttf"}, {"Identity": "C:\\Users\\<USER>\\source\\repos\\Almutasim90\\AspCoreMVC6\\AspCoreMVC6\\wwwroot\\fonts\\Tajawal-Medium.ttf", "SourceId": "AspCoreMVC6", "SourceType": "Discovered", "ContentRoot": "C:\\Users\\<USER>\\source\\repos\\Almutasim90\\AspCoreMVC6\\AspCoreMVC6\\wwwroot\\", "BasePath": "_content/AspCoreMVC6", "RelativePath": "fonts/Tajawal-Medium.ttf", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "RelatedAsset": "", "AssetTraitName": "", "AssetTraitValue": "", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\fonts\\Tajawal-Medium.ttf"}, {"Identity": "C:\\Users\\<USER>\\source\\repos\\Almutasim90\\AspCoreMVC6\\AspCoreMVC6\\wwwroot\\fonts\\Tajawal-Regular.ttf", "SourceId": "AspCoreMVC6", "SourceType": "Discovered", "ContentRoot": "C:\\Users\\<USER>\\source\\repos\\Almutasim90\\AspCoreMVC6\\AspCoreMVC6\\wwwroot\\", "BasePath": "_content/AspCoreMVC6", "RelativePath": "fonts/Tajawal-Regular.ttf", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "RelatedAsset": "", "AssetTraitName": "", "AssetTraitValue": "", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\fonts\\Tajawal-Regular.ttf"}, {"Identity": "C:\\Users\\<USER>\\source\\repos\\Almutasim90\\AspCoreMVC6\\AspCoreMVC6\\wwwroot\\js\\site.js", "SourceId": "AspCoreMVC6", "SourceType": "Discovered", "ContentRoot": "C:\\Users\\<USER>\\source\\repos\\Almutasim90\\AspCoreMVC6\\AspCoreMVC6\\wwwroot\\", "BasePath": "_content/AspCoreMVC6", "RelativePath": "js/site.js", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "RelatedAsset": "", "AssetTraitName": "", "AssetTraitValue": "", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\js\\site.js"}, {"Identity": "C:\\Users\\<USER>\\source\\repos\\Almutasim90\\AspCoreMVC6\\AspCoreMVC6\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap.css", "SourceId": "AspCoreMVC6", "SourceType": "Discovered", "ContentRoot": "C:\\Users\\<USER>\\source\\repos\\Almutasim90\\AspCoreMVC6\\AspCoreMVC6\\wwwroot\\", "BasePath": "_content/AspCoreMVC6", "RelativePath": "lib/bootstrap/dist/css/bootstrap.css", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "RelatedAsset": "", "AssetTraitName": "", "AssetTraitValue": "", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap.css"}, {"Identity": "C:\\Users\\<USER>\\source\\repos\\Almutasim90\\AspCoreMVC6\\AspCoreMVC6\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap.css.map", "SourceId": "AspCoreMVC6", "SourceType": "Discovered", "ContentRoot": "C:\\Users\\<USER>\\source\\repos\\Almutasim90\\AspCoreMVC6\\AspCoreMVC6\\wwwroot\\", "BasePath": "_content/AspCoreMVC6", "RelativePath": "lib/bootstrap/dist/css/bootstrap.css.map", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "RelatedAsset": "", "AssetTraitName": "", "AssetTraitValue": "", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap.css.map"}, {"Identity": "C:\\Users\\<USER>\\source\\repos\\Almutasim90\\AspCoreMVC6\\AspCoreMVC6\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap.min.css", "SourceId": "AspCoreMVC6", "SourceType": "Discovered", "ContentRoot": "C:\\Users\\<USER>\\source\\repos\\Almutasim90\\AspCoreMVC6\\AspCoreMVC6\\wwwroot\\", "BasePath": "_content/AspCoreMVC6", "RelativePath": "lib/bootstrap/dist/css/bootstrap.min.css", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "RelatedAsset": "", "AssetTraitName": "", "AssetTraitValue": "", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap.min.css"}, {"Identity": "C:\\Users\\<USER>\\source\\repos\\Almutasim90\\AspCoreMVC6\\AspCoreMVC6\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap.min.css.map", "SourceId": "AspCoreMVC6", "SourceType": "Discovered", "ContentRoot": "C:\\Users\\<USER>\\source\\repos\\Almutasim90\\AspCoreMVC6\\AspCoreMVC6\\wwwroot\\", "BasePath": "_content/AspCoreMVC6", "RelativePath": "lib/bootstrap/dist/css/bootstrap.min.css.map", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "RelatedAsset": "", "AssetTraitName": "", "AssetTraitValue": "", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap.min.css.map"}, {"Identity": "C:\\Users\\<USER>\\source\\repos\\Almutasim90\\AspCoreMVC6\\AspCoreMVC6\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap.rtl.css", "SourceId": "AspCoreMVC6", "SourceType": "Discovered", "ContentRoot": "C:\\Users\\<USER>\\source\\repos\\Almutasim90\\AspCoreMVC6\\AspCoreMVC6\\wwwroot\\", "BasePath": "_content/AspCoreMVC6", "RelativePath": "lib/bootstrap/dist/css/bootstrap.rtl.css", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "RelatedAsset": "", "AssetTraitName": "", "AssetTraitValue": "", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap.rtl.css"}, {"Identity": "C:\\Users\\<USER>\\source\\repos\\Almutasim90\\AspCoreMVC6\\AspCoreMVC6\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap.rtl.css.map", "SourceId": "AspCoreMVC6", "SourceType": "Discovered", "ContentRoot": "C:\\Users\\<USER>\\source\\repos\\Almutasim90\\AspCoreMVC6\\AspCoreMVC6\\wwwroot\\", "BasePath": "_content/AspCoreMVC6", "RelativePath": "lib/bootstrap/dist/css/bootstrap.rtl.css.map", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "RelatedAsset": "", "AssetTraitName": "", "AssetTraitValue": "", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap.rtl.css.map"}, {"Identity": "C:\\Users\\<USER>\\source\\repos\\Almutasim90\\AspCoreMVC6\\AspCoreMVC6\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap.rtl.min.css", "SourceId": "AspCoreMVC6", "SourceType": "Discovered", "ContentRoot": "C:\\Users\\<USER>\\source\\repos\\Almutasim90\\AspCoreMVC6\\AspCoreMVC6\\wwwroot\\", "BasePath": "_content/AspCoreMVC6", "RelativePath": "lib/bootstrap/dist/css/bootstrap.rtl.min.css", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "RelatedAsset": "", "AssetTraitName": "", "AssetTraitValue": "", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap.rtl.min.css"}, {"Identity": "C:\\Users\\<USER>\\source\\repos\\Almutasim90\\AspCoreMVC6\\AspCoreMVC6\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap.rtl.min.css.map", "SourceId": "AspCoreMVC6", "SourceType": "Discovered", "ContentRoot": "C:\\Users\\<USER>\\source\\repos\\Almutasim90\\AspCoreMVC6\\AspCoreMVC6\\wwwroot\\", "BasePath": "_content/AspCoreMVC6", "RelativePath": "lib/bootstrap/dist/css/bootstrap.rtl.min.css.map", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "RelatedAsset": "", "AssetTraitName": "", "AssetTraitValue": "", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap.rtl.min.css.map"}, {"Identity": "C:\\Users\\<USER>\\source\\repos\\Almutasim90\\AspCoreMVC6\\AspCoreMVC6\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-grid.css", "SourceId": "AspCoreMVC6", "SourceType": "Discovered", "ContentRoot": "C:\\Users\\<USER>\\source\\repos\\Almutasim90\\AspCoreMVC6\\AspCoreMVC6\\wwwroot\\", "BasePath": "_content/AspCoreMVC6", "RelativePath": "lib/bootstrap/dist/css/bootstrap-grid.css", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "RelatedAsset": "", "AssetTraitName": "", "AssetTraitValue": "", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-grid.css"}, {"Identity": "C:\\Users\\<USER>\\source\\repos\\Almutasim90\\AspCoreMVC6\\AspCoreMVC6\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-grid.css.map", "SourceId": "AspCoreMVC6", "SourceType": "Discovered", "ContentRoot": "C:\\Users\\<USER>\\source\\repos\\Almutasim90\\AspCoreMVC6\\AspCoreMVC6\\wwwroot\\", "BasePath": "_content/AspCoreMVC6", "RelativePath": "lib/bootstrap/dist/css/bootstrap-grid.css.map", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "RelatedAsset": "", "AssetTraitName": "", "AssetTraitValue": "", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-grid.css.map"}, {"Identity": "C:\\Users\\<USER>\\source\\repos\\Almutasim90\\AspCoreMVC6\\AspCoreMVC6\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-grid.min.css", "SourceId": "AspCoreMVC6", "SourceType": "Discovered", "ContentRoot": "C:\\Users\\<USER>\\source\\repos\\Almutasim90\\AspCoreMVC6\\AspCoreMVC6\\wwwroot\\", "BasePath": "_content/AspCoreMVC6", "RelativePath": "lib/bootstrap/dist/css/bootstrap-grid.min.css", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "RelatedAsset": "", "AssetTraitName": "", "AssetTraitValue": "", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-grid.min.css"}, {"Identity": "C:\\Users\\<USER>\\source\\repos\\Almutasim90\\AspCoreMVC6\\AspCoreMVC6\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-grid.min.css.map", "SourceId": "AspCoreMVC6", "SourceType": "Discovered", "ContentRoot": "C:\\Users\\<USER>\\source\\repos\\Almutasim90\\AspCoreMVC6\\AspCoreMVC6\\wwwroot\\", "BasePath": "_content/AspCoreMVC6", "RelativePath": "lib/bootstrap/dist/css/bootstrap-grid.min.css.map", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "RelatedAsset": "", "AssetTraitName": "", "AssetTraitValue": "", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-grid.min.css.map"}, {"Identity": "C:\\Users\\<USER>\\source\\repos\\Almutasim90\\AspCoreMVC6\\AspCoreMVC6\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-grid.rtl.css", "SourceId": "AspCoreMVC6", "SourceType": "Discovered", "ContentRoot": "C:\\Users\\<USER>\\source\\repos\\Almutasim90\\AspCoreMVC6\\AspCoreMVC6\\wwwroot\\", "BasePath": "_content/AspCoreMVC6", "RelativePath": "lib/bootstrap/dist/css/bootstrap-grid.rtl.css", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "RelatedAsset": "", "AssetTraitName": "", "AssetTraitValue": "", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-grid.rtl.css"}, {"Identity": "C:\\Users\\<USER>\\source\\repos\\Almutasim90\\AspCoreMVC6\\AspCoreMVC6\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-grid.rtl.css.map", "SourceId": "AspCoreMVC6", "SourceType": "Discovered", "ContentRoot": "C:\\Users\\<USER>\\source\\repos\\Almutasim90\\AspCoreMVC6\\AspCoreMVC6\\wwwroot\\", "BasePath": "_content/AspCoreMVC6", "RelativePath": "lib/bootstrap/dist/css/bootstrap-grid.rtl.css.map", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "RelatedAsset": "", "AssetTraitName": "", "AssetTraitValue": "", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-grid.rtl.css.map"}, {"Identity": "C:\\Users\\<USER>\\source\\repos\\Almutasim90\\AspCoreMVC6\\AspCoreMVC6\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-grid.rtl.min.css", "SourceId": "AspCoreMVC6", "SourceType": "Discovered", "ContentRoot": "C:\\Users\\<USER>\\source\\repos\\Almutasim90\\AspCoreMVC6\\AspCoreMVC6\\wwwroot\\", "BasePath": "_content/AspCoreMVC6", "RelativePath": "lib/bootstrap/dist/css/bootstrap-grid.rtl.min.css", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "RelatedAsset": "", "AssetTraitName": "", "AssetTraitValue": "", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-grid.rtl.min.css"}, {"Identity": "C:\\Users\\<USER>\\source\\repos\\Almutasim90\\AspCoreMVC6\\AspCoreMVC6\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-grid.rtl.min.css.map", "SourceId": "AspCoreMVC6", "SourceType": "Discovered", "ContentRoot": "C:\\Users\\<USER>\\source\\repos\\Almutasim90\\AspCoreMVC6\\AspCoreMVC6\\wwwroot\\", "BasePath": "_content/AspCoreMVC6", "RelativePath": "lib/bootstrap/dist/css/bootstrap-grid.rtl.min.css.map", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "RelatedAsset": "", "AssetTraitName": "", "AssetTraitValue": "", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-grid.rtl.min.css.map"}, {"Identity": "C:\\Users\\<USER>\\source\\repos\\Almutasim90\\AspCoreMVC6\\AspCoreMVC6\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-reboot.css", "SourceId": "AspCoreMVC6", "SourceType": "Discovered", "ContentRoot": "C:\\Users\\<USER>\\source\\repos\\Almutasim90\\AspCoreMVC6\\AspCoreMVC6\\wwwroot\\", "BasePath": "_content/AspCoreMVC6", "RelativePath": "lib/bootstrap/dist/css/bootstrap-reboot.css", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "RelatedAsset": "", "AssetTraitName": "", "AssetTraitValue": "", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-reboot.css"}, {"Identity": "C:\\Users\\<USER>\\source\\repos\\Almutasim90\\AspCoreMVC6\\AspCoreMVC6\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-reboot.css.map", "SourceId": "AspCoreMVC6", "SourceType": "Discovered", "ContentRoot": "C:\\Users\\<USER>\\source\\repos\\Almutasim90\\AspCoreMVC6\\AspCoreMVC6\\wwwroot\\", "BasePath": "_content/AspCoreMVC6", "RelativePath": "lib/bootstrap/dist/css/bootstrap-reboot.css.map", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "RelatedAsset": "", "AssetTraitName": "", "AssetTraitValue": "", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-reboot.css.map"}, {"Identity": "C:\\Users\\<USER>\\source\\repos\\Almutasim90\\AspCoreMVC6\\AspCoreMVC6\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-reboot.min.css", "SourceId": "AspCoreMVC6", "SourceType": "Discovered", "ContentRoot": "C:\\Users\\<USER>\\source\\repos\\Almutasim90\\AspCoreMVC6\\AspCoreMVC6\\wwwroot\\", "BasePath": "_content/AspCoreMVC6", "RelativePath": "lib/bootstrap/dist/css/bootstrap-reboot.min.css", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "RelatedAsset": "", "AssetTraitName": "", "AssetTraitValue": "", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-reboot.min.css"}, {"Identity": "C:\\Users\\<USER>\\source\\repos\\Almutasim90\\AspCoreMVC6\\AspCoreMVC6\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-reboot.min.css.map", "SourceId": "AspCoreMVC6", "SourceType": "Discovered", "ContentRoot": "C:\\Users\\<USER>\\source\\repos\\Almutasim90\\AspCoreMVC6\\AspCoreMVC6\\wwwroot\\", "BasePath": "_content/AspCoreMVC6", "RelativePath": "lib/bootstrap/dist/css/bootstrap-reboot.min.css.map", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "RelatedAsset": "", "AssetTraitName": "", "AssetTraitValue": "", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-reboot.min.css.map"}, {"Identity": "C:\\Users\\<USER>\\source\\repos\\Almutasim90\\AspCoreMVC6\\AspCoreMVC6\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-reboot.rtl.css", "SourceId": "AspCoreMVC6", "SourceType": "Discovered", "ContentRoot": "C:\\Users\\<USER>\\source\\repos\\Almutasim90\\AspCoreMVC6\\AspCoreMVC6\\wwwroot\\", "BasePath": "_content/AspCoreMVC6", "RelativePath": "lib/bootstrap/dist/css/bootstrap-reboot.rtl.css", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "RelatedAsset": "", "AssetTraitName": "", "AssetTraitValue": "", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-reboot.rtl.css"}, {"Identity": "C:\\Users\\<USER>\\source\\repos\\Almutasim90\\AspCoreMVC6\\AspCoreMVC6\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-reboot.rtl.css.map", "SourceId": "AspCoreMVC6", "SourceType": "Discovered", "ContentRoot": "C:\\Users\\<USER>\\source\\repos\\Almutasim90\\AspCoreMVC6\\AspCoreMVC6\\wwwroot\\", "BasePath": "_content/AspCoreMVC6", "RelativePath": "lib/bootstrap/dist/css/bootstrap-reboot.rtl.css.map", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "RelatedAsset": "", "AssetTraitName": "", "AssetTraitValue": "", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-reboot.rtl.css.map"}, {"Identity": "C:\\Users\\<USER>\\source\\repos\\Almutasim90\\AspCoreMVC6\\AspCoreMVC6\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-reboot.rtl.min.css", "SourceId": "AspCoreMVC6", "SourceType": "Discovered", "ContentRoot": "C:\\Users\\<USER>\\source\\repos\\Almutasim90\\AspCoreMVC6\\AspCoreMVC6\\wwwroot\\", "BasePath": "_content/AspCoreMVC6", "RelativePath": "lib/bootstrap/dist/css/bootstrap-reboot.rtl.min.css", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "RelatedAsset": "", "AssetTraitName": "", "AssetTraitValue": "", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-reboot.rtl.min.css"}, {"Identity": "C:\\Users\\<USER>\\source\\repos\\Almutasim90\\AspCoreMVC6\\AspCoreMVC6\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-reboot.rtl.min.css.map", "SourceId": "AspCoreMVC6", "SourceType": "Discovered", "ContentRoot": "C:\\Users\\<USER>\\source\\repos\\Almutasim90\\AspCoreMVC6\\AspCoreMVC6\\wwwroot\\", "BasePath": "_content/AspCoreMVC6", "RelativePath": "lib/bootstrap/dist/css/bootstrap-reboot.rtl.min.css.map", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "RelatedAsset": "", "AssetTraitName": "", "AssetTraitValue": "", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-reboot.rtl.min.css.map"}, {"Identity": "C:\\Users\\<USER>\\source\\repos\\Almutasim90\\AspCoreMVC6\\AspCoreMVC6\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-utilities.css", "SourceId": "AspCoreMVC6", "SourceType": "Discovered", "ContentRoot": "C:\\Users\\<USER>\\source\\repos\\Almutasim90\\AspCoreMVC6\\AspCoreMVC6\\wwwroot\\", "BasePath": "_content/AspCoreMVC6", "RelativePath": "lib/bootstrap/dist/css/bootstrap-utilities.css", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "RelatedAsset": "", "AssetTraitName": "", "AssetTraitValue": "", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-utilities.css"}, {"Identity": "C:\\Users\\<USER>\\source\\repos\\Almutasim90\\AspCoreMVC6\\AspCoreMVC6\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-utilities.css.map", "SourceId": "AspCoreMVC6", "SourceType": "Discovered", "ContentRoot": "C:\\Users\\<USER>\\source\\repos\\Almutasim90\\AspCoreMVC6\\AspCoreMVC6\\wwwroot\\", "BasePath": "_content/AspCoreMVC6", "RelativePath": "lib/bootstrap/dist/css/bootstrap-utilities.css.map", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "RelatedAsset": "", "AssetTraitName": "", "AssetTraitValue": "", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-utilities.css.map"}, {"Identity": "C:\\Users\\<USER>\\source\\repos\\Almutasim90\\AspCoreMVC6\\AspCoreMVC6\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-utilities.min.css", "SourceId": "AspCoreMVC6", "SourceType": "Discovered", "ContentRoot": "C:\\Users\\<USER>\\source\\repos\\Almutasim90\\AspCoreMVC6\\AspCoreMVC6\\wwwroot\\", "BasePath": "_content/AspCoreMVC6", "RelativePath": "lib/bootstrap/dist/css/bootstrap-utilities.min.css", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "RelatedAsset": "", "AssetTraitName": "", "AssetTraitValue": "", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-utilities.min.css"}, {"Identity": "C:\\Users\\<USER>\\source\\repos\\Almutasim90\\AspCoreMVC6\\AspCoreMVC6\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-utilities.min.css.map", "SourceId": "AspCoreMVC6", "SourceType": "Discovered", "ContentRoot": "C:\\Users\\<USER>\\source\\repos\\Almutasim90\\AspCoreMVC6\\AspCoreMVC6\\wwwroot\\", "BasePath": "_content/AspCoreMVC6", "RelativePath": "lib/bootstrap/dist/css/bootstrap-utilities.min.css.map", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "RelatedAsset": "", "AssetTraitName": "", "AssetTraitValue": "", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-utilities.min.css.map"}, {"Identity": "C:\\Users\\<USER>\\source\\repos\\Almutasim90\\AspCoreMVC6\\AspCoreMVC6\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-utilities.rtl.css", "SourceId": "AspCoreMVC6", "SourceType": "Discovered", "ContentRoot": "C:\\Users\\<USER>\\source\\repos\\Almutasim90\\AspCoreMVC6\\AspCoreMVC6\\wwwroot\\", "BasePath": "_content/AspCoreMVC6", "RelativePath": "lib/bootstrap/dist/css/bootstrap-utilities.rtl.css", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "RelatedAsset": "", "AssetTraitName": "", "AssetTraitValue": "", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-utilities.rtl.css"}, {"Identity": "C:\\Users\\<USER>\\source\\repos\\Almutasim90\\AspCoreMVC6\\AspCoreMVC6\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-utilities.rtl.css.map", "SourceId": "AspCoreMVC6", "SourceType": "Discovered", "ContentRoot": "C:\\Users\\<USER>\\source\\repos\\Almutasim90\\AspCoreMVC6\\AspCoreMVC6\\wwwroot\\", "BasePath": "_content/AspCoreMVC6", "RelativePath": "lib/bootstrap/dist/css/bootstrap-utilities.rtl.css.map", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "RelatedAsset": "", "AssetTraitName": "", "AssetTraitValue": "", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-utilities.rtl.css.map"}, {"Identity": "C:\\Users\\<USER>\\source\\repos\\Almutasim90\\AspCoreMVC6\\AspCoreMVC6\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-utilities.rtl.min.css", "SourceId": "AspCoreMVC6", "SourceType": "Discovered", "ContentRoot": "C:\\Users\\<USER>\\source\\repos\\Almutasim90\\AspCoreMVC6\\AspCoreMVC6\\wwwroot\\", "BasePath": "_content/AspCoreMVC6", "RelativePath": "lib/bootstrap/dist/css/bootstrap-utilities.rtl.min.css", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "RelatedAsset": "", "AssetTraitName": "", "AssetTraitValue": "", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-utilities.rtl.min.css"}, {"Identity": "C:\\Users\\<USER>\\source\\repos\\Almutasim90\\AspCoreMVC6\\AspCoreMVC6\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-utilities.rtl.min.css.map", "SourceId": "AspCoreMVC6", "SourceType": "Discovered", "ContentRoot": "C:\\Users\\<USER>\\source\\repos\\Almutasim90\\AspCoreMVC6\\AspCoreMVC6\\wwwroot\\", "BasePath": "_content/AspCoreMVC6", "RelativePath": "lib/bootstrap/dist/css/bootstrap-utilities.rtl.min.css.map", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "RelatedAsset": "", "AssetTraitName": "", "AssetTraitValue": "", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-utilities.rtl.min.css.map"}, {"Identity": "C:\\Users\\<USER>\\source\\repos\\Almutasim90\\AspCoreMVC6\\AspCoreMVC6\\wwwroot\\lib\\bootstrap\\dist\\js\\bootstrap.bundle.js", "SourceId": "AspCoreMVC6", "SourceType": "Discovered", "ContentRoot": "C:\\Users\\<USER>\\source\\repos\\Almutasim90\\AspCoreMVC6\\AspCoreMVC6\\wwwroot\\", "BasePath": "_content/AspCoreMVC6", "RelativePath": "lib/bootstrap/dist/js/bootstrap.bundle.js", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "RelatedAsset": "", "AssetTraitName": "", "AssetTraitValue": "", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\lib\\bootstrap\\dist\\js\\bootstrap.bundle.js"}, {"Identity": "C:\\Users\\<USER>\\source\\repos\\Almutasim90\\AspCoreMVC6\\AspCoreMVC6\\wwwroot\\lib\\bootstrap\\dist\\js\\bootstrap.bundle.js.map", "SourceId": "AspCoreMVC6", "SourceType": "Discovered", "ContentRoot": "C:\\Users\\<USER>\\source\\repos\\Almutasim90\\AspCoreMVC6\\AspCoreMVC6\\wwwroot\\", "BasePath": "_content/AspCoreMVC6", "RelativePath": "lib/bootstrap/dist/js/bootstrap.bundle.js.map", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "RelatedAsset": "", "AssetTraitName": "", "AssetTraitValue": "", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\lib\\bootstrap\\dist\\js\\bootstrap.bundle.js.map"}, {"Identity": "C:\\Users\\<USER>\\source\\repos\\Almutasim90\\AspCoreMVC6\\AspCoreMVC6\\wwwroot\\lib\\bootstrap\\dist\\js\\bootstrap.bundle.min.js", "SourceId": "AspCoreMVC6", "SourceType": "Discovered", "ContentRoot": "C:\\Users\\<USER>\\source\\repos\\Almutasim90\\AspCoreMVC6\\AspCoreMVC6\\wwwroot\\", "BasePath": "_content/AspCoreMVC6", "RelativePath": "lib/bootstrap/dist/js/bootstrap.bundle.min.js", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "RelatedAsset": "", "AssetTraitName": "", "AssetTraitValue": "", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\lib\\bootstrap\\dist\\js\\bootstrap.bundle.min.js"}, {"Identity": "C:\\Users\\<USER>\\source\\repos\\Almutasim90\\AspCoreMVC6\\AspCoreMVC6\\wwwroot\\lib\\bootstrap\\dist\\js\\bootstrap.bundle.min.js.map", "SourceId": "AspCoreMVC6", "SourceType": "Discovered", "ContentRoot": "C:\\Users\\<USER>\\source\\repos\\Almutasim90\\AspCoreMVC6\\AspCoreMVC6\\wwwroot\\", "BasePath": "_content/AspCoreMVC6", "RelativePath": "lib/bootstrap/dist/js/bootstrap.bundle.min.js.map", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "RelatedAsset": "", "AssetTraitName": "", "AssetTraitValue": "", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\lib\\bootstrap\\dist\\js\\bootstrap.bundle.min.js.map"}, {"Identity": "C:\\Users\\<USER>\\source\\repos\\Almutasim90\\AspCoreMVC6\\AspCoreMVC6\\wwwroot\\lib\\bootstrap\\dist\\js\\bootstrap.esm.js", "SourceId": "AspCoreMVC6", "SourceType": "Discovered", "ContentRoot": "C:\\Users\\<USER>\\source\\repos\\Almutasim90\\AspCoreMVC6\\AspCoreMVC6\\wwwroot\\", "BasePath": "_content/AspCoreMVC6", "RelativePath": "lib/bootstrap/dist/js/bootstrap.esm.js", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "RelatedAsset": "", "AssetTraitName": "", "AssetTraitValue": "", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\lib\\bootstrap\\dist\\js\\bootstrap.esm.js"}, {"Identity": "C:\\Users\\<USER>\\source\\repos\\Almutasim90\\AspCoreMVC6\\AspCoreMVC6\\wwwroot\\lib\\bootstrap\\dist\\js\\bootstrap.esm.js.map", "SourceId": "AspCoreMVC6", "SourceType": "Discovered", "ContentRoot": "C:\\Users\\<USER>\\source\\repos\\Almutasim90\\AspCoreMVC6\\AspCoreMVC6\\wwwroot\\", "BasePath": "_content/AspCoreMVC6", "RelativePath": "lib/bootstrap/dist/js/bootstrap.esm.js.map", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "RelatedAsset": "", "AssetTraitName": "", "AssetTraitValue": "", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\lib\\bootstrap\\dist\\js\\bootstrap.esm.js.map"}, {"Identity": "C:\\Users\\<USER>\\source\\repos\\Almutasim90\\AspCoreMVC6\\AspCoreMVC6\\wwwroot\\lib\\bootstrap\\dist\\js\\bootstrap.esm.min.js", "SourceId": "AspCoreMVC6", "SourceType": "Discovered", "ContentRoot": "C:\\Users\\<USER>\\source\\repos\\Almutasim90\\AspCoreMVC6\\AspCoreMVC6\\wwwroot\\", "BasePath": "_content/AspCoreMVC6", "RelativePath": "lib/bootstrap/dist/js/bootstrap.esm.min.js", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "RelatedAsset": "", "AssetTraitName": "", "AssetTraitValue": "", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\lib\\bootstrap\\dist\\js\\bootstrap.esm.min.js"}, {"Identity": "C:\\Users\\<USER>\\source\\repos\\Almutasim90\\AspCoreMVC6\\AspCoreMVC6\\wwwroot\\lib\\bootstrap\\dist\\js\\bootstrap.esm.min.js.map", "SourceId": "AspCoreMVC6", "SourceType": "Discovered", "ContentRoot": "C:\\Users\\<USER>\\source\\repos\\Almutasim90\\AspCoreMVC6\\AspCoreMVC6\\wwwroot\\", "BasePath": "_content/AspCoreMVC6", "RelativePath": "lib/bootstrap/dist/js/bootstrap.esm.min.js.map", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "RelatedAsset": "", "AssetTraitName": "", "AssetTraitValue": "", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\lib\\bootstrap\\dist\\js\\bootstrap.esm.min.js.map"}, {"Identity": "C:\\Users\\<USER>\\source\\repos\\Almutasim90\\AspCoreMVC6\\AspCoreMVC6\\wwwroot\\lib\\bootstrap\\dist\\js\\bootstrap.js", "SourceId": "AspCoreMVC6", "SourceType": "Discovered", "ContentRoot": "C:\\Users\\<USER>\\source\\repos\\Almutasim90\\AspCoreMVC6\\AspCoreMVC6\\wwwroot\\", "BasePath": "_content/AspCoreMVC6", "RelativePath": "lib/bootstrap/dist/js/bootstrap.js", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "RelatedAsset": "", "AssetTraitName": "", "AssetTraitValue": "", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\lib\\bootstrap\\dist\\js\\bootstrap.js"}, {"Identity": "C:\\Users\\<USER>\\source\\repos\\Almutasim90\\AspCoreMVC6\\AspCoreMVC6\\wwwroot\\lib\\bootstrap\\dist\\js\\bootstrap.js.map", "SourceId": "AspCoreMVC6", "SourceType": "Discovered", "ContentRoot": "C:\\Users\\<USER>\\source\\repos\\Almutasim90\\AspCoreMVC6\\AspCoreMVC6\\wwwroot\\", "BasePath": "_content/AspCoreMVC6", "RelativePath": "lib/bootstrap/dist/js/bootstrap.js.map", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "RelatedAsset": "", "AssetTraitName": "", "AssetTraitValue": "", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\lib\\bootstrap\\dist\\js\\bootstrap.js.map"}, {"Identity": "C:\\Users\\<USER>\\source\\repos\\Almutasim90\\AspCoreMVC6\\AspCoreMVC6\\wwwroot\\lib\\bootstrap\\dist\\js\\bootstrap.min.js", "SourceId": "AspCoreMVC6", "SourceType": "Discovered", "ContentRoot": "C:\\Users\\<USER>\\source\\repos\\Almutasim90\\AspCoreMVC6\\AspCoreMVC6\\wwwroot\\", "BasePath": "_content/AspCoreMVC6", "RelativePath": "lib/bootstrap/dist/js/bootstrap.min.js", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "RelatedAsset": "", "AssetTraitName": "", "AssetTraitValue": "", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\lib\\bootstrap\\dist\\js\\bootstrap.min.js"}, {"Identity": "C:\\Users\\<USER>\\source\\repos\\Almutasim90\\AspCoreMVC6\\AspCoreMVC6\\wwwroot\\lib\\bootstrap\\dist\\js\\bootstrap.min.js.map", "SourceId": "AspCoreMVC6", "SourceType": "Discovered", "ContentRoot": "C:\\Users\\<USER>\\source\\repos\\Almutasim90\\AspCoreMVC6\\AspCoreMVC6\\wwwroot\\", "BasePath": "_content/AspCoreMVC6", "RelativePath": "lib/bootstrap/dist/js/bootstrap.min.js.map", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "RelatedAsset": "", "AssetTraitName": "", "AssetTraitValue": "", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\lib\\bootstrap\\dist\\js\\bootstrap.min.js.map"}, {"Identity": "C:\\Users\\<USER>\\source\\repos\\Almutasim90\\AspCoreMVC6\\AspCoreMVC6\\wwwroot\\lib\\bootstrap\\LICENSE", "SourceId": "AspCoreMVC6", "SourceType": "Discovered", "ContentRoot": "C:\\Users\\<USER>\\source\\repos\\Almutasim90\\AspCoreMVC6\\AspCoreMVC6\\wwwroot\\", "BasePath": "_content/AspCoreMVC6", "RelativePath": "lib/bootstrap/LICENSE", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "RelatedAsset": "", "AssetTraitName": "", "AssetTraitValue": "", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\lib\\bootstrap\\LICENSE"}, {"Identity": "C:\\Users\\<USER>\\source\\repos\\Almutasim90\\AspCoreMVC6\\AspCoreMVC6\\wwwroot\\lib\\jquery\\dist\\jquery.js", "SourceId": "AspCoreMVC6", "SourceType": "Discovered", "ContentRoot": "C:\\Users\\<USER>\\source\\repos\\Almutasim90\\AspCoreMVC6\\AspCoreMVC6\\wwwroot\\", "BasePath": "_content/AspCoreMVC6", "RelativePath": "lib/jquery/dist/jquery.js", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "RelatedAsset": "", "AssetTraitName": "", "AssetTraitValue": "", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\lib\\jquery\\dist\\jquery.js"}, {"Identity": "C:\\Users\\<USER>\\source\\repos\\Almutasim90\\AspCoreMVC6\\AspCoreMVC6\\wwwroot\\lib\\jquery\\dist\\jquery.min.js", "SourceId": "AspCoreMVC6", "SourceType": "Discovered", "ContentRoot": "C:\\Users\\<USER>\\source\\repos\\Almutasim90\\AspCoreMVC6\\AspCoreMVC6\\wwwroot\\", "BasePath": "_content/AspCoreMVC6", "RelativePath": "lib/jquery/dist/jquery.min.js", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "RelatedAsset": "", "AssetTraitName": "", "AssetTraitValue": "", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\lib\\jquery\\dist\\jquery.min.js"}, {"Identity": "C:\\Users\\<USER>\\source\\repos\\Almutasim90\\AspCoreMVC6\\AspCoreMVC6\\wwwroot\\lib\\jquery\\dist\\jquery.min.map", "SourceId": "AspCoreMVC6", "SourceType": "Discovered", "ContentRoot": "C:\\Users\\<USER>\\source\\repos\\Almutasim90\\AspCoreMVC6\\AspCoreMVC6\\wwwroot\\", "BasePath": "_content/AspCoreMVC6", "RelativePath": "lib/jquery/dist/jquery.min.map", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "RelatedAsset": "", "AssetTraitName": "", "AssetTraitValue": "", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\lib\\jquery\\dist\\jquery.min.map"}, {"Identity": "C:\\Users\\<USER>\\source\\repos\\Almutasim90\\AspCoreMVC6\\AspCoreMVC6\\wwwroot\\lib\\jquery\\LICENSE.txt", "SourceId": "AspCoreMVC6", "SourceType": "Discovered", "ContentRoot": "C:\\Users\\<USER>\\source\\repos\\Almutasim90\\AspCoreMVC6\\AspCoreMVC6\\wwwroot\\", "BasePath": "_content/AspCoreMVC6", "RelativePath": "lib/jquery/LICENSE.txt", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "RelatedAsset": "", "AssetTraitName": "", "AssetTraitValue": "", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\lib\\jquery\\LICENSE.txt"}, {"Identity": "C:\\Users\\<USER>\\source\\repos\\Almutasim90\\AspCoreMVC6\\AspCoreMVC6\\wwwroot\\lib\\jquery-validation\\dist\\additional-methods.js", "SourceId": "AspCoreMVC6", "SourceType": "Discovered", "ContentRoot": "C:\\Users\\<USER>\\source\\repos\\Almutasim90\\AspCoreMVC6\\AspCoreMVC6\\wwwroot\\", "BasePath": "_content/AspCoreMVC6", "RelativePath": "lib/jquery-validation/dist/additional-methods.js", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "RelatedAsset": "", "AssetTraitName": "", "AssetTraitValue": "", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\lib\\jquery-validation\\dist\\additional-methods.js"}, {"Identity": "C:\\Users\\<USER>\\source\\repos\\Almutasim90\\AspCoreMVC6\\AspCoreMVC6\\wwwroot\\lib\\jquery-validation\\dist\\additional-methods.min.js", "SourceId": "AspCoreMVC6", "SourceType": "Discovered", "ContentRoot": "C:\\Users\\<USER>\\source\\repos\\Almutasim90\\AspCoreMVC6\\AspCoreMVC6\\wwwroot\\", "BasePath": "_content/AspCoreMVC6", "RelativePath": "lib/jquery-validation/dist/additional-methods.min.js", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "RelatedAsset": "", "AssetTraitName": "", "AssetTraitValue": "", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\lib\\jquery-validation\\dist\\additional-methods.min.js"}, {"Identity": "C:\\Users\\<USER>\\source\\repos\\Almutasim90\\AspCoreMVC6\\AspCoreMVC6\\wwwroot\\lib\\jquery-validation\\dist\\jquery.validate.js", "SourceId": "AspCoreMVC6", "SourceType": "Discovered", "ContentRoot": "C:\\Users\\<USER>\\source\\repos\\Almutasim90\\AspCoreMVC6\\AspCoreMVC6\\wwwroot\\", "BasePath": "_content/AspCoreMVC6", "RelativePath": "lib/jquery-validation/dist/jquery.validate.js", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "RelatedAsset": "", "AssetTraitName": "", "AssetTraitValue": "", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\lib\\jquery-validation\\dist\\jquery.validate.js"}, {"Identity": "C:\\Users\\<USER>\\source\\repos\\Almutasim90\\AspCoreMVC6\\AspCoreMVC6\\wwwroot\\lib\\jquery-validation\\dist\\jquery.validate.min.js", "SourceId": "AspCoreMVC6", "SourceType": "Discovered", "ContentRoot": "C:\\Users\\<USER>\\source\\repos\\Almutasim90\\AspCoreMVC6\\AspCoreMVC6\\wwwroot\\", "BasePath": "_content/AspCoreMVC6", "RelativePath": "lib/jquery-validation/dist/jquery.validate.min.js", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "RelatedAsset": "", "AssetTraitName": "", "AssetTraitValue": "", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\lib\\jquery-validation\\dist\\jquery.validate.min.js"}, {"Identity": "C:\\Users\\<USER>\\source\\repos\\Almutasim90\\AspCoreMVC6\\AspCoreMVC6\\wwwroot\\lib\\jquery-validation\\LICENSE.md", "SourceId": "AspCoreMVC6", "SourceType": "Discovered", "ContentRoot": "C:\\Users\\<USER>\\source\\repos\\Almutasim90\\AspCoreMVC6\\AspCoreMVC6\\wwwroot\\", "BasePath": "_content/AspCoreMVC6", "RelativePath": "lib/jquery-validation/LICENSE.md", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "RelatedAsset": "", "AssetTraitName": "", "AssetTraitValue": "", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\lib\\jquery-validation\\LICENSE.md"}, {"Identity": "C:\\Users\\<USER>\\source\\repos\\Almutasim90\\AspCoreMVC6\\AspCoreMVC6\\wwwroot\\lib\\jquery-validation-unobtrusive\\jquery.validate.unobtrusive.js", "SourceId": "AspCoreMVC6", "SourceType": "Discovered", "ContentRoot": "C:\\Users\\<USER>\\source\\repos\\Almutasim90\\AspCoreMVC6\\AspCoreMVC6\\wwwroot\\", "BasePath": "_content/AspCoreMVC6", "RelativePath": "lib/jquery-validation-unobtrusive/jquery.validate.unobtrusive.js", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "RelatedAsset": "", "AssetTraitName": "", "AssetTraitValue": "", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\lib\\jquery-validation-unobtrusive\\jquery.validate.unobtrusive.js"}, {"Identity": "C:\\Users\\<USER>\\source\\repos\\Almutasim90\\AspCoreMVC6\\AspCoreMVC6\\wwwroot\\lib\\jquery-validation-unobtrusive\\jquery.validate.unobtrusive.min.js", "SourceId": "AspCoreMVC6", "SourceType": "Discovered", "ContentRoot": "C:\\Users\\<USER>\\source\\repos\\Almutasim90\\AspCoreMVC6\\AspCoreMVC6\\wwwroot\\", "BasePath": "_content/AspCoreMVC6", "RelativePath": "lib/jquery-validation-unobtrusive/jquery.validate.unobtrusive.min.js", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "RelatedAsset": "", "AssetTraitName": "", "AssetTraitValue": "", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\lib\\jquery-validation-unobtrusive\\jquery.validate.unobtrusive.min.js"}, {"Identity": "C:\\Users\\<USER>\\source\\repos\\Almutasim90\\AspCoreMVC6\\AspCoreMVC6\\wwwroot\\lib\\jquery-validation-unobtrusive\\LICENSE.txt", "SourceId": "AspCoreMVC6", "SourceType": "Discovered", "ContentRoot": "C:\\Users\\<USER>\\source\\repos\\Almutasim90\\AspCoreMVC6\\AspCoreMVC6\\wwwroot\\", "BasePath": "_content/AspCoreMVC6", "RelativePath": "lib/jquery-validation-unobtrusive/LICENSE.txt", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "RelatedAsset": "", "AssetTraitName": "", "AssetTraitValue": "", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\lib\\jquery-validation-unobtrusive\\LICENSE.txt"}]}