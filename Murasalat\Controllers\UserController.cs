using Microsoft.AspNetCore.Mvc;
using Recruitment.Models;

namespace Recruitment.Controllers
{
    public class UserController : Controller
    {
        public IActionResult Index()
        {
            // ...existing code...
            return View();
        }

        public IActionResult Create()
        {
            // ...existing code...
            return View();
        }

        [HttpPost]
        public IActionResult Create(User user)
        {
            // ...existing code...
            return RedirectToAction("Index");
        }

        public IActionResult Edit(int id)
        {
            // ...existing code...
            return View();
        }

        [HttpPost]
        public IActionResult Edit(User user)
        {
            // ...existing code...
            return RedirectToAction("Index");
        }

        public IActionResult Delete(int id)
        {
            // ...existing code...
            return View();
        }

        [HttpPost, ActionName("Delete")]
        public IActionResult DeleteConfirmed(int id)
        {
            // ...existing code...
            return RedirectToAction("Index");
        }
    }
}
