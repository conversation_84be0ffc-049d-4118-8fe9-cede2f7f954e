﻿@model OmanInternship.ViewModel.UserInfoViewModel

@{
    var studentNIC = Context.Session.GetString("StudentNIC");
    var recruiterCR = Context.Session.GetString("RecruiterCR");
    var employeeNIC = Context.Session.GetString("EmployeeNIC");
}


<div class="container mt-2 mb-1">
    @if (Model == null)
    {
        <div class="card shadow">
            <div class="card-header">
                <h6>User Information</h6>
            </div>
            <div class="card-body">
                <div class="alert alert-warning">
                    User information is not available.
                </div>
            </div>
        </div>
    }
    else
    {
        <div class="card shadow">
            <div class="card-header">
                <h4 class="p-1 mt-1">User Information</h4>
            </div>
            <div class="card-body">
                <div class="row">
                    @if (!string.IsNullOrEmpty(studentNIC))
                    {
                        <div class="col-md-4">
                            <p class="card-text"><strong>Name:</strong> @Model.Name</p>
                            <p class="card-text"><strong>NIC:</strong> @Model.NIC</p>
                            <p class="card-text"><strong>Major:</strong> @Model.MajorName</p>
                        </div>
                        <div class="col-md-4">
                            <p class="card-text"><strong>Study Country:</strong> @Model.Country</p>
                        </div>
                        <div class="col-md-4">
                            <p class="card-text"><strong>GPA:</strong> @Model.GPA</p>
                        </div>
                    }
                    else if (!string.IsNullOrEmpty(recruiterCR))
                    {
                        <div class="col-md-4">
                            <p class="card-text"><strong>CR:</strong> @Model.CR</p>
                            <p class="card-text"><strong>Organization Name:</strong> @Model.OrganizationName</p>
                        </div>
                        <div class="col-md-4">
                            <p class="card-text"><strong>Legal Type:</strong> @Model.LegalType</p>
                            <p class="card-text"><strong>Contact Number:</strong> @Model.ContactNumber</p>
                        </div>
                        <div class="col-md-4">
                            <p class="card-text"><strong>Address:</strong> @Model.Address</p>
                        </div>
                    }
                    else if (!string.IsNullOrEmpty(employeeNIC))
                    {
                        <div class="col-md-4">
                            <p class="card-text"><strong>Name:</strong> @Model.EmployeeName</p>
                            <p class="card-text"><strong>NIC:</strong> @Model.EmployeeNIC</p>
                            <p class="card-text"><strong>Profile Number:</strong> @Model.ProfileNumber</p>
                        </div>
                        <div class="col-md-4">
                            <!-- Additional employee information if available -->
                        </div>
                        <div class="col-md-4">
                            <!-- Additional employee information if available -->
                        </div>
                    }
                </div>
            </div>
        </div>
    }
</div>

<style>
    body {
        font-family: 'Roboto Slab';
    }
</style>
