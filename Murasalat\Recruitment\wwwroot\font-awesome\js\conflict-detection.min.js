/*!
 * Font Awesome Free 6.7.2 by @fontawesome - https://fontawesome.com
 * License - https://fontawesome.com/license/free (Icons: CC BY 4.0, Fonts: SIL OFL 1.1, Code: MIT License)
 * Copyright 2024 Fonticons, Inc.
 */
(t=>{("object"!=typeof exports||"undefined"==typeof module)&&"function"==typeof define&&define.amd?define(t):t()})(function(){function o(t,e,n){var o;(e="symbol"==typeof(o=((t,e)=>{if("object"!=typeof t||!t)return t;var n=t[Symbol.toPrimitive];if(void 0===n)return("string"===e?String:Number)(t);if("object"!=typeof(n=n.call(t,e||"default")))return n;throw new TypeError("@@toPrimitive must return a primitive value.")})(e,"string"))?o:o+"")in t?Object.defineProperty(t,e,{value:n,enumerable:!0,configurable:!0,writable:!0}):t[e]=n}function r(e,t){var n,o=Object.keys(e);return Object.getOwnPropertySymbols&&(n=Object.getOwnPropertySymbols(e),t&&(n=n.filter(function(t){return Object.getOwnPropertyDescriptor(e,t).enumerable})),o.push.apply(o,n)),o}function s(e){for(var t=1;t<arguments.length;t++){var n=null!=arguments[t]?arguments[t]:{};t%2?r(Object(n),!0).forEach(function(t){o(e,t,n[t])}):Object.getOwnPropertyDescriptors?Object.defineProperties(e,Object.getOwnPropertyDescriptors(n)):r(Object(n)).forEach(function(t){Object.defineProperty(e,t,Object.getOwnPropertyDescriptor(n,t))})}return e}let t={},e={};try{"undefined"!=typeof window&&(t=window),"undefined"!=typeof document&&(e=document)}catch(t){}var{userAgent:n=""}=t.navigator||{};let l=t,d=e,a=!!l.document,i=!!d.documentElement&&!!d.head&&"function"==typeof d.addEventListener&&"function"==typeof d.createElement;~n.indexOf("MSIE")||n.indexOf("Trident/");function f(){d.removeEventListener("DOMContentLoaded",f),u=1,c.map(t=>t())}let c=[],u=!1;function h(t){i&&(u?setTimeout(t,0):c.push(t))}i&&!(u=(d.documentElement.doScroll?/^loaded|^c/:/^loaded|^i|^c/).test(d.readyState))&&d.addEventListener("DOMContentLoaded",f);var b="undefined"!=typeof window?window:"undefined"!=typeof global?global:"undefined"!=typeof self?self:{};(function(t){function d(t,e){var n=(65535&t)+(65535&e);return(t>>16)+(e>>16)+(n>>16)<<16|65535&n}function s(t,e,n,o,r,a){return d((e=d(d(e,t),d(o,a)))<<r|e>>>32-r,n)}function u(t,e,n,o,r,a,i){return s(e&n|~e&o,t,e,r,a,i)}function h(t,e,n,o,r,a,i){return s(e&o|n&~o,t,e,r,a,i)}function g(t,e,n,o,r,a,i){return s(e^n^o,t,e,r,a,i)}function m(t,e,n,o,r,a,i){return s(n^(e|~o),t,e,r,a,i)}function f(t,e){t[e>>5]|=128<<e%32,t[14+(e+64>>>9<<4)]=e;for(var n,o,r,a,i=1732584193,s=-271733879,f=-1732584194,c=271733878,l=0;l<t.length;l+=16)i=u(n=i,o=s,r=f,a=c,t[l],7,-680876936),c=u(c,i,s,f,t[l+1],12,-389564586),f=u(f,c,i,s,t[l+2],17,606105819),s=u(s,f,c,i,t[l+3],22,-1044525330),i=u(i,s,f,c,t[l+4],7,-176418897),c=u(c,i,s,f,t[l+5],12,1200080426),f=u(f,c,i,s,t[l+6],17,-1473231341),s=u(s,f,c,i,t[l+7],22,-45705983),i=u(i,s,f,c,t[l+8],7,1770035416),c=u(c,i,s,f,t[l+9],12,-1958414417),f=u(f,c,i,s,t[l+10],17,-42063),s=u(s,f,c,i,t[l+11],22,-1990404162),i=u(i,s,f,c,t[l+12],7,1804603682),c=u(c,i,s,f,t[l+13],12,-40341101),f=u(f,c,i,s,t[l+14],17,-1502002290),i=h(i,s=u(s,f,c,i,t[l+15],22,1236535329),f,c,t[l+1],5,-165796510),c=h(c,i,s,f,t[l+6],9,-1069501632),f=h(f,c,i,s,t[l+11],14,643717713),s=h(s,f,c,i,t[l],20,-373897302),i=h(i,s,f,c,t[l+5],5,-701558691),c=h(c,i,s,f,t[l+10],9,38016083),f=h(f,c,i,s,t[l+15],14,-660478335),s=h(s,f,c,i,t[l+4],20,-405537848),i=h(i,s,f,c,t[l+9],5,568446438),c=h(c,i,s,f,t[l+14],9,-1019803690),f=h(f,c,i,s,t[l+3],14,-187363961),s=h(s,f,c,i,t[l+8],20,1163531501),i=h(i,s,f,c,t[l+13],5,-1444681467),c=h(c,i,s,f,t[l+2],9,-51403784),f=h(f,c,i,s,t[l+7],14,1735328473),i=g(i,s=h(s,f,c,i,t[l+12],20,-1926607734),f,c,t[l+5],4,-378558),c=g(c,i,s,f,t[l+8],11,-2022574463),f=g(f,c,i,s,t[l+11],16,1839030562),s=g(s,f,c,i,t[l+14],23,-35309556),i=g(i,s,f,c,t[l+1],4,-1530992060),c=g(c,i,s,f,t[l+4],11,1272893353),f=g(f,c,i,s,t[l+7],16,-155497632),s=g(s,f,c,i,t[l+10],23,-1094730640),i=g(i,s,f,c,t[l+13],4,681279174),c=g(c,i,s,f,t[l],11,-358537222),f=g(f,c,i,s,t[l+3],16,-722521979),s=g(s,f,c,i,t[l+6],23,76029189),i=g(i,s,f,c,t[l+9],4,-640364487),c=g(c,i,s,f,t[l+12],11,-421815835),f=g(f,c,i,s,t[l+15],16,530742520),i=m(i,s=g(s,f,c,i,t[l+2],23,-995338651),f,c,t[l],6,-198630844),c=m(c,i,s,f,t[l+7],10,1126891415),f=m(f,c,i,s,t[l+14],15,-1416354905),s=m(s,f,c,i,t[l+5],21,-57434055),i=m(i,s,f,c,t[l+12],6,1700485571),c=m(c,i,s,f,t[l+3],10,-1894986606),f=m(f,c,i,s,t[l+10],15,-1051523),s=m(s,f,c,i,t[l+1],21,-2054922799),i=m(i,s,f,c,t[l+8],6,1873313359),c=m(c,i,s,f,t[l+15],10,-30611744),f=m(f,c,i,s,t[l+6],15,-1560198380),s=m(s,f,c,i,t[l+13],21,1309151649),i=m(i,s,f,c,t[l+4],6,-145523070),c=m(c,i,s,f,t[l+11],10,-1120210379),f=m(f,c,i,s,t[l+2],15,718787259),s=m(s,f,c,i,t[l+9],21,-343485551),i=d(i,n),s=d(s,o),f=d(f,r),c=d(c,a);return[i,s,f,c]}function c(t){for(var e="",n=32*t.length,o=0;o<n;o+=8)e+=String.fromCharCode(t[o>>5]>>>o%32&255);return e}function l(t){var e=[];for(e[(t.length>>2)-1]=void 0,o=0;o<e.length;o+=1)e[o]=0;for(var n=8*t.length,o=0;o<n;o+=8)e[o>>5]|=(255&t.charCodeAt(o/8))<<o%32;return e}function o(t){for(var e,n="0123456789abcdef",o="",r=0;r<t.length;r+=1)e=t.charCodeAt(r),o+=n.charAt(e>>>4&15)+n.charAt(15&e);return o}function p(t){return unescape(encodeURIComponent(t))}function r(t){return c(f(l(t=p(t)),8*t.length))}function a(t,e){var n,o,t=p(t),e=p(e),r=l(t),a=[],i=[];for(a[15]=i[15]=void 0,16<r.length&&(r=f(r,8*t.length)),n=0;n<16;n+=1)a[n]=909522486^r[n],i[n]=1549556828^r[n];return o=f(a.concat(l(e)),512+8*e.length),c(f(i.concat(o),640))}function e(t,e,n){return e?n?a(e,t):o(a(e,t)):n?r(t):o(r(t))}var n;n=b,t.exports?t.exports=e:n.md5=e})(g={exports:{}});var g,m=g.exports;function p(t){if(null!==t&&"object"==typeof t)return t.src?m(t.src):t.href?m(t.href):t.innerText&&""!==t.innerText?m(t.innerText):void 0}let w="fa-kits-diag",y="fa-kits-node-under-test",v="data-md5",A="data-fa-detection-ignore",x="data-fa-detection-timeout",T="data-fa-detection-results-collection-max-wait",D=t=>{t.preventDefault(),t.stopPropagation()};function E(t){let{fn:a=()=>!0,initialDuration:e=1,maxDuration:i=l.FontAwesomeDetection.timeout,showProgress:s=!1,progressIndicator:f}=t;return new Promise(function(o,r){!function e(t,n){setTimeout(function(){var t=a();s&&console.info(f),t?o(t):(t=250+n)<=i?e(250,t):r("timeout")},t)}(e,0)})}function O(t){var{nodesTested:e,nodesFound:n}=t;l.FontAwesomeDetection=l.FontAwesomeDetection||{},l.FontAwesomeDetection.nodesTested=e,l.FontAwesomeDetection.nodesFound=n,l.FontAwesomeDetection.detectionDone=!0}function C(t){let e=0<arguments.length&&void 0!==t?t:()=>{},n={conflict:{},noConflict:{}};l.onmessage=function(t){"file://"!==l.location.origin&&t.origin!==l.location.origin||t&&t.data&&("fontawesome-conflict"===t.data.type?n.conflict[t.data.md5]=t.data:"no-conflict"===t.data.type&&(n.noConflict[t.data.md5]=t.data))};var o=(e=>{var o=Array.from(d.scripts).filter(t=>!t.hasAttribute(A)&&t!==e),r={};for(let s=0;s<o.length;s++){let t=d.createElement("iframe"),e=(t.setAttribute("style","display:none;"),d.createElement("script"));e.setAttribute("id",y);var a=p(o[s]);e.setAttribute(v,a),r[a]=o[s],""!==o[s].src&&(e.src=o[s].src),""!==o[s].innerText&&(e.innerText=o[s].innerText),e.async=!0;let n=d.createElement("script");n.setAttribute("id",w);var i="file://"===l.location.origin?"*":l.location.origin;n.innerText="(".concat(((n,o,r)=>{parent.FontAwesomeDetection.__pollUntil({fn:()=>!!window.FontAwesomeConfig||!!window.FontAwesomeKitConfig}).then(function(){var t=document.getElementById(n);parent.postMessage({type:"fontawesome-conflict",technology:"js",src:t.src,innerText:t.innerText,tagName:t.tagName,md5:o},r)}).catch(function(t){var e=document.getElementById(n);"timeout"===t?parent.postMessage({type:"no-conflict",src:e.src,innerText:e.innerText,tagName:e.tagName,md5:o},r):console.error(t)})}).toString(),")('").concat(y,"', '").concat(a,"', '").concat(i,"');"),t.onload=function(){t.contentWindow.addEventListener("error",D,!0),t.contentDocument.head.appendChild(n),t.contentDocument.head.appendChild(e)},h(()=>d.body.appendChild(t))}return r})(d.currentScript),r=(()=>{var t=Array.from(d.getElementsByTagName("link")).filter(t=>!t.hasAttribute(A)),e=Array.from(d.getElementsByTagName("style")).filter(t=>!(t.hasAttribute(A)||l.FontAwesomeConfig&&t.innerText.match(new RegExp("svg:not\\(:root\\)\\.".concat(l.FontAwesomeConfig.replacementClass)))));function n(t,e){let n=d.createElement("iframe");n.setAttribute("style","visibility: hidden; position: absolute; height: 0; width: 0;");var o="fa-test-icon-"+e;let r=d.createElement("i"),a=(r.setAttribute("class","fa fa-coffee"),r.setAttribute("id",o),d.createElement("script"));a.setAttribute("id",w);var i="file://"===l.location.origin?"*":l.location.origin;a.innerText="(".concat(((n,e,o,r)=>{parent.FontAwesomeDetection.__pollUntil({fn:()=>{var t=document.getElementById(e),t=window.getComputedStyle(t).getPropertyValue("font-family");return!(!t.match(/FontAwesome/)&&!t.match(/Font Awesome [56]/))}}).then(()=>{var t=document.getElementById(n);parent.postMessage({type:"fontawesome-conflict",technology:"webfont",href:t.href,innerText:t.innerText,tagName:t.tagName,md5:o},r)}).catch(function(t){var e=document.getElementById(n);"timeout"===t?parent.postMessage({type:"no-conflict",technology:"webfont",href:e.src,innerText:e.innerText,tagName:e.tagName,md5:o},r):console.error(t)})}).toString(),")('").concat(y,"', '").concat(o||"foo","', '").concat(e,"', '").concat(i,"');"),n.onload=function(){n.contentWindow.addEventListener("error",D,!0),n.contentDocument.head.appendChild(a),n.contentDocument.head.appendChild(t),n.contentDocument.body.appendChild(r)},h(()=>d.body.appendChild(n))}var o={};for(let f=0;f<t.length;f++){var r=d.createElement("link"),a=(r.setAttribute("id",y),r.setAttribute("href",t[f].href),r.setAttribute("rel",t[f].rel),p(t[f]));r.setAttribute(v,a),o[a]=t[f],n(r,a)}for(let c=0;c<e.length;c++){var i=d.createElement("style"),s=(i.setAttribute("id",y),p(e[c]));i.setAttribute(v,s),i.innerText=e[c].innerText,o[s]=e[c],n(i,s)}return o})();let a=s(s({},o),r),i=Object.keys(o).length+Object.keys(r).length;o=l.FontAwesomeDetection.timeout+l.FontAwesomeDetection.resultsCollectionMaxWait;console.group("Font Awesome Detector"),0===i?(console.info("%cAll Good!","color: green; font-size: large"),console.info("We didn't find anything that needs testing for conflicts. Ergo, no conflicts.")):(console.info("Testing ".concat(i," possible conflicts.")),console.info("We'll wait about ".concat(Math.round(l.FontAwesomeDetection.timeout/10)/100," seconds while testing these and\n")+"then up to another ".concat(Math.round(l.FontAwesomeDetection.resultsCollectionMaxWait/10)/100," to allow the browser time\n")+"to accumulate the results. But we'll probably be outta here way before then.\n\n"),console.info("You can adjust those durations by assigning values to these attributes on the <script> element that loads this detection:"),console.info("\t%c".concat(x,"%c: milliseconds to wait for each test before deciding whether it's a conflict."),"font-weight: bold;","font-size: normal;"),console.info("\t%c".concat(T,"%c: milliseconds to wait for the browser to accumulate test results before giving up."),"font-weight: bold;","font-size: normal;"),E({maxDuration:o,showProgress:!0,progressIndicator:"waiting...",fn:()=>Object.keys(n.conflict).length+Object.keys(n.noConflict).length>=i}).then(()=>{console.info("DONE!"),O({nodesTested:n,nodesFound:a}),e({nodesTested:n,nodesFound:a}),console.groupEnd()}).catch(t=>{"timeout"===t?console.info("TIME OUT! We waited until we got tired. Here's what we found:"):(console.info("Whoops! We hit an error:",t),console.info("Here's what we'd found up until that error:")),O({nodesTested:n,nodesFound:a}),e({nodesTested:n,nodesFound:a}),console.groupEnd()}))}var n=l.FontAwesomeDetection||{},n=s(s(s({},{report:function(t){var e,{nodesTested:n,nodesFound:o}=t,r={};for(e in o)n.conflict[e]||n.noConflict[e]||(r[e]=o[e]);var a=Object.keys(n.conflict).length;if(0<a){console.info("%cConflict".concat(1<a?"s":""," found:"),"color: darkred; font-size: large");var i,s={};for(i in n.conflict){var f=n.conflict[i];s[i]={tagName:f.tagName,"src/href":f.src||f.href||"n/a","innerText excerpt":f.innerText&&""!==f.innerText?f.innerText.slice(0,200)+"...":"(empty)"}}console.table(s)}if(0<(a=Object.keys(n.noConflict).length)){console.info("%cNo conflict".concat(1<a?"s":""," found with ").concat(1===a?"this":"these",":"),"color: green; font-size: large");var c,l={};for(c in n.noConflict){var d=n.noConflict[c];l[c]={tagName:d.tagName,"src/href":d.src||d.href||"n/a","innerText excerpt":d.innerText&&""!==d.innerText?d.innerText.slice(0,200)+"...":"(empty)"}}console.table(l)}if(0<(a=Object.keys(r).length)){console.info("%cLeftovers--we timed out before collecting test results for ".concat(1===a?"this":"these",":"),"color: blue; font-size: large");var u,h={};for(u in r){var g=r[u];h[u]={tagName:g.tagName,"src/href":g.src||g.href||"n/a","innerText excerpt":g.innerText&&""!==g.innerText?g.innerText.slice(0,200)+"...":"(empty)"}}console.table(h)}},timeout:+(d.currentScript.getAttribute(x)||"2000"),resultsCollectionMaxWait:+(d.currentScript.getAttribute(T)||"5000")}),n),{},{__pollUntil:E,md5ForNode:p,detectionDone:!1,nodesTested:null,nodesFound:null}),n=(l.FontAwesomeDetection=n,{classic:{fa:"solid",fas:"solid","fa-solid":"solid",far:"regular","fa-regular":"regular",fal:"light","fa-light":"light",fat:"thin","fa-thin":"thin",fab:"brands","fa-brands":"brands"},duotone:{fa:"solid",fad:"solid","fa-solid":"solid","fa-duotone":"solid",fadr:"regular","fa-regular":"regular",fadl:"light","fa-light":"light",fadt:"thin","fa-thin":"thin"},sharp:{fa:"solid",fass:"solid","fa-solid":"solid",fasr:"regular","fa-regular":"regular",fasl:"light","fa-light":"light",fast:"thin","fa-thin":"thin"},"sharp-duotone":{fa:"solid",fasds:"solid","fa-solid":"solid",fasdr:"regular","fa-regular":"regular",fasdl:"light","fa-light":"light",fasdt:"thin","fa-thin":"thin"}}),F="classic",k={fak:"kit","fa-kit":"kit"},j={fakd:"kit-duotone","fa-kit-duotone":"kit-duotone"},N={kit:"fak"},P={"kit-duotone":"fakd"};let S=(()=>{try{return"production"===process.env.NODE_ENV}catch(t){return!1}})();function M(t){return new Proxy(t,{get(t,e){return e in t?t[e]:t[F]}})}var I=s({},n),n=(I[F]=s(s(s(s({},{"fa-duotone":"duotone"}),n[F]),k),j),M(I),s({},{classic:{solid:"fas",regular:"far",light:"fal",thin:"fat",brands:"fab"},duotone:{solid:"fad",regular:"fadr",light:"fadl",thin:"fadt"},sharp:{solid:"fass",regular:"fasr",light:"fasl",thin:"fast"},"sharp-duotone":{solid:"fasds",regular:"fasdr",light:"fasdl",thin:"fasdt"}})),k=(n[F]=s(s(s(s({},{duotone:"fad"}),n[F]),N),P),M(n),s({},{classic:{fab:"fa-brands",fad:"fa-duotone",fal:"fa-light",far:"fa-regular",fas:"fa-solid",fat:"fa-thin"},duotone:{fadr:"fa-regular",fadl:"fa-light",fadt:"fa-thin"},sharp:{fass:"fa-solid",fasr:"fa-regular",fasl:"fa-light",fast:"fa-thin"},"sharp-duotone":{fasds:"fa-solid",fasdr:"fa-regular",fasdl:"fa-light",fasdt:"fa-thin"}})),j=(k[F]=s(s({},k[F]),{fak:"fa-kit"}),M(k),s({},{classic:{"fa-brands":"fab","fa-duotone":"fad","fa-light":"fal","fa-regular":"far","fa-solid":"fas","fa-thin":"fat"},duotone:{"fa-regular":"fadr","fa-light":"fadl","fa-thin":"fadt"},sharp:{"fa-solid":"fass","fa-regular":"fasr","fa-light":"fasl","fa-thin":"fast"},"sharp-duotone":{"fa-solid":"fasds","fa-regular":"fasdr","fa-light":"fasdl","fa-thin":"fasdt"}}));j[F]=s(s({},j[F]),{"fa-kit":"fak"}),M(j),M(s({},{classic:{900:"fas",400:"far",normal:"far",300:"fal",100:"fat"},duotone:{900:"fad",400:"fadr",300:"fadl",100:"fadt"},sharp:{900:"fass",400:"fasr",300:"fasl",100:"fast"},"sharp-duotone":{900:"fasds",400:"fasdr",300:"fasdl",100:"fasdt"}}));!function(t){try{for(var e=arguments.length,n=new Array(1<e?e-1:0),o=1;o<e;o++)n[o-1]=arguments[o];t(...n)}catch(t){if(!S)throw t}}(()=>{a&&i&&C(window.FontAwesomeDetection.report)})});