﻿@model OmanInternship.Models.City

@{
    ViewData["Title"] = "Create";
    Layout = "~/Views/Shared/_Layout.cshtml";
   
}



<hr />
<div class="row">
    <div class="col-md-4">
        <form asp-action="Create">
            <div asp-validation-summary="ModelOnly" class="text-danger"></div>

            <!-- CityName Input Field with Syncfusion TextBox -->
            <div class="form-group">
                <label asp-for="CityName" class="control-label"></label>
                <input asp-for="CityName" id="CityName" name="CityName" class="e-input" />
                <span asp-validation-for="CityName" class="text-danger"></span>
            </div>

            <!-- LocationId DropDown List with Syncfusion DropDownList -->
            <div class="form-group">
                <label asp-for="LocationId" class="control-label"></label>
                <select asp-for="LocationId" id="LocationId" name="LocationId" class="e-dropdownlist" asp-items="ViewBag.LocationId"></select>
            </div>

            <!-- Submit Button with Syncfusion style -->
            <div class="form-group">
                <input type="submit" value="Create" class="e-btn e-primary" />
            </div>
        </form>
    </div>
</div>

<div>
    <a asp-action="Index" class="e-link">Back to List</a>
</div>
