﻿@model OmanInternship.Models.City

@{
    ViewData["Title"] = "Details";
    Layout = "~/Views/Shared/_Layout.cshtml";
}

<h1>Details</h1>

<div>
    <h4>City</h4>
    <hr />
    <dl class="row">
        <dt class = "col-sm-2">
            @Html.DisplayNameFor(model => model.CityName)
        </dt>
        <dd class = "col-sm-10">
            @Html.DisplayFor(model => model.CityName)
        </dd>
        <dt class = "col-sm-2">
            @Html.DisplayNameFor(model => model.Location)
        </dt>
        <dd class = "col-sm-10">
            @Html.DisplayFor(model => model.Location.LocationName)
        </dd>
    </dl>
</div>
<div>
    <a asp-action="Edit" asp-route-id="@Model?.CityId">Edit</a> |
    <a asp-action="Index">Back to List</a>
</div>
