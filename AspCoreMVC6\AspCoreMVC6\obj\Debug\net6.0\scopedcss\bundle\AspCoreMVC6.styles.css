/* _content/AspCoreMVC6/Views/Shared/_Layout.cshtml.rz.scp.css */
/* Please see documentation at https://docs.microsoft.com/aspnet/core/client-side/bundling-and-minification
for details on configuring this project to bundle and minify static web assets. */

a.navbar-brand[b-gms4mgn3ar] {
  white-space: normal;
  text-align: center;
  word-break: break-all;
}

a[b-gms4mgn3ar] {
  color: #0077cc;
}

.btn-primary[b-gms4mgn3ar] {
  color: #fff;
  background-color: #1b6ec2;
  border-color: #1861ac;
}

.nav-pills .nav-link.active[b-gms4mgn3ar], .nav-pills .show > .nav-link[b-gms4mgn3ar] {
  color: #fff;
  background-color: #1b6ec2;
  border-color: #1861ac;
}

.border-top[b-gms4mgn3ar] {
  border-top: 1px solid #e5e5e5;
}
.border-bottom[b-gms4mgn3ar] {
  border-bottom: 1px solid #e5e5e5;
}

.box-shadow[b-gms4mgn3ar] {
  box-shadow: 0 .25rem .75rem rgba(0, 0, 0, .05);
}

button.accept-policy[b-gms4mgn3ar] {
  font-size: 1rem;
  line-height: inherit;
}

.footer[b-gms4mgn3ar] {
  position: absolute;
  bottom: 0;
  width: 100%;
  white-space: nowrap;
  line-height: 60px;
}
