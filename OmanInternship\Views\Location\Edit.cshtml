﻿@model OmanInternship.Models.Location

@{
    ViewData["Title"] = "Edit";
    Layout = "~/Views/Shared/_Layout.cshtml";

}

<h1>Edit</h1>

<h4>Location</h4>
<hr />
<div class="row">
    <div class="col-md-4">
        <form asp-action="Edit">
            <div asp-validation-summary="ModelOnly" class="text-danger"></div>
            <input type="hidden" asp-for="id" />
            <div class="form-group">
                <label asp-for="LocationName" class="control-label"></label>
                <input asp-for="LocationName" class="form-control" />
                <span asp-validation-for="LocationName" class="text-danger"></span>
            </div>
            <div class="form-group">
                <input type="submit" value="Save" class="btn btn-primary" />
            </div>
        </form>
    </div>
</div>

<div>
    <a asp-action="Index">Back to List</a>
</div>

