using System.ComponentModel.DataAnnotations;

namespace Recruitment.Models.ViewModels
{
    public class LoginViewModel
    {
        [Required(ErrorMessage = "ادخل الرقم العسكري")]
        [Display(Name = "الرقم العسكري ")]


        public string? ServiceNumber { get; set; }


        [Display(Name = " الاسم ")]
        public string? FullName { get; set; }


        [Required(ErrorMessage = "كلمة المرور مطلوبة")]
        [DataType(DataType.Password)]
        [Display(Name = "كلمة المرور")]
        [StringLength(100, ErrorMessage = "كلمة المرور يجب أن تكون على الأقل {2} أحرف", MinimumLength = 3)]
        public string? Password { get; set; }

        [Display(Name = "تذكرني")]
        public bool RememberMe { get; set; }

    }
}