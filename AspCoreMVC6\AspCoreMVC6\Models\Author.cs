﻿using System.ComponentModel.DataAnnotations;

namespace AspCoreMVC6.Models
{
    public class Author
    {
        [Key]
        [ScaffoldColumn(false)]
        public int AuthorId { get; set; }
        [Display(Name = "المؤلف")]
        [MaxLength(15,ErrorMessage ="{0} is 15")]
        [Required(ErrorMessage ="{0} is Required")]
       
        
        public string ? AuthorName { get; set; }
    }
}
