{"Files": [{"Id": "C:\\Users\\<USER>\\source\\repos\\Almutasim90\\Murasalat\\Recruitment\\obj\\Debug\\net9.0\\scopedcss\\projectbundle\\Murasalat.bundle.scp.css", "PackagePath": "staticwebassets\\Murasalat.ldtkbkeq3j.bundle.scp.css"}, {"Id": "C:\\Users\\<USER>\\source\\repos\\Almutasim90\\Murasalat\\Recruitment\\wwwroot\\Images\\rafo_logo.png", "PackagePath": "staticwebassets\\Images\\rafo_logo.png"}, {"Id": "C:\\Users\\<USER>\\source\\repos\\Almutasim90\\Murasalat\\Recruitment\\wwwroot\\css\\sidebar.css", "PackagePath": "staticwebassets\\css\\sidebar.css"}, {"Id": "C:\\Users\\<USER>\\source\\repos\\Almutasim90\\Murasalat\\Recruitment\\wwwroot\\css\\site.css", "PackagePath": "staticwebassets\\css\\site.css"}, {"Id": "C:\\Users\\<USER>\\source\\repos\\Almutasim90\\Murasalat\\Recruitment\\wwwroot\\datatables.net-bs5\\datatables.bootstrap5.css", "PackagePath": "staticwebassets\\datatables.net-bs5\\datatables.bootstrap5.css"}, {"Id": "C:\\Users\\<USER>\\source\\repos\\Almutasim90\\Murasalat\\Recruitment\\wwwroot\\datatables.net-bs5\\datatables.bootstrap5.js", "PackagePath": "staticwebassets\\datatables.net-bs5\\datatables.bootstrap5.js"}, {"Id": "C:\\Users\\<USER>\\source\\repos\\Almutasim90\\Murasalat\\Recruitment\\wwwroot\\datatables.net-bs5\\datatables.bootstrap5.min.css", "PackagePath": "staticwebassets\\datatables.net-bs5\\datatables.bootstrap5.min.css"}, {"Id": "C:\\Users\\<USER>\\source\\repos\\Almutasim90\\Murasalat\\Recruitment\\wwwroot\\datatables.net-bs5\\datatables.bootstrap5.min.js", "PackagePath": "staticwebassets\\datatables.net-bs5\\datatables.bootstrap5.min.js"}, {"Id": "C:\\Users\\<USER>\\source\\repos\\Almutasim90\\Murasalat\\Recruitment\\wwwroot\\favicon.ico", "PackagePath": "staticwebassets\\favicon.ico"}, {"Id": "C:\\Users\\<USER>\\source\\repos\\Almutasim90\\Murasalat\\Recruitment\\wwwroot\\font-awesome\\css\\all.css", "PackagePath": "staticwebassets\\font-awesome\\css\\all.css"}, {"Id": "C:\\Users\\<USER>\\source\\repos\\Almutasim90\\Murasalat\\Recruitment\\wwwroot\\font-awesome\\css\\all.min.css", "PackagePath": "staticwebassets\\font-awesome\\css\\all.min.css"}, {"Id": "C:\\Users\\<USER>\\source\\repos\\Almutasim90\\Murasalat\\Recruitment\\wwwroot\\font-awesome\\css\\brands.css", "PackagePath": "staticwebassets\\font-awesome\\css\\brands.css"}, {"Id": "C:\\Users\\<USER>\\source\\repos\\Almutasim90\\Murasalat\\Recruitment\\wwwroot\\font-awesome\\css\\brands.min.css", "PackagePath": "staticwebassets\\font-awesome\\css\\brands.min.css"}, {"Id": "C:\\Users\\<USER>\\source\\repos\\Almutasim90\\Murasalat\\Recruitment\\wwwroot\\font-awesome\\css\\fontawesome.css", "PackagePath": "staticwebassets\\font-awesome\\css\\fontawesome.css"}, {"Id": "C:\\Users\\<USER>\\source\\repos\\Almutasim90\\Murasalat\\Recruitment\\wwwroot\\font-awesome\\css\\fontawesome.min.css", "PackagePath": "staticwebassets\\font-awesome\\css\\fontawesome.min.css"}, {"Id": "C:\\Users\\<USER>\\source\\repos\\Almutasim90\\Murasalat\\Recruitment\\wwwroot\\font-awesome\\css\\regular.css", "PackagePath": "staticwebassets\\font-awesome\\css\\regular.css"}, {"Id": "C:\\Users\\<USER>\\source\\repos\\Almutasim90\\Murasalat\\Recruitment\\wwwroot\\font-awesome\\css\\regular.min.css", "PackagePath": "staticwebassets\\font-awesome\\css\\regular.min.css"}, {"Id": "C:\\Users\\<USER>\\source\\repos\\Almutasim90\\Murasalat\\Recruitment\\wwwroot\\font-awesome\\css\\solid.css", "PackagePath": "staticwebassets\\font-awesome\\css\\solid.css"}, {"Id": "C:\\Users\\<USER>\\source\\repos\\Almutasim90\\Murasalat\\Recruitment\\wwwroot\\font-awesome\\css\\solid.min.css", "PackagePath": "staticwebassets\\font-awesome\\css\\solid.min.css"}, {"Id": "C:\\Users\\<USER>\\source\\repos\\Almutasim90\\Murasalat\\Recruitment\\wwwroot\\font-awesome\\css\\svg-with-js.css", "PackagePath": "staticwebassets\\font-awesome\\css\\svg-with-js.css"}, {"Id": "C:\\Users\\<USER>\\source\\repos\\Almutasim90\\Murasalat\\Recruitment\\wwwroot\\font-awesome\\css\\svg-with-js.min.css", "PackagePath": "staticwebassets\\font-awesome\\css\\svg-with-js.min.css"}, {"Id": "C:\\Users\\<USER>\\source\\repos\\Almutasim90\\Murasalat\\Recruitment\\wwwroot\\font-awesome\\css\\v4-font-face.css", "PackagePath": "staticwebassets\\font-awesome\\css\\v4-font-face.css"}, {"Id": "C:\\Users\\<USER>\\source\\repos\\Almutasim90\\Murasalat\\Recruitment\\wwwroot\\font-awesome\\css\\v4-font-face.min.css", "PackagePath": "staticwebassets\\font-awesome\\css\\v4-font-face.min.css"}, {"Id": "C:\\Users\\<USER>\\source\\repos\\Almutasim90\\Murasalat\\Recruitment\\wwwroot\\font-awesome\\css\\v4-shims.css", "PackagePath": "staticwebassets\\font-awesome\\css\\v4-shims.css"}, {"Id": "C:\\Users\\<USER>\\source\\repos\\Almutasim90\\Murasalat\\Recruitment\\wwwroot\\font-awesome\\css\\v4-shims.min.css", "PackagePath": "staticwebassets\\font-awesome\\css\\v4-shims.min.css"}, {"Id": "C:\\Users\\<USER>\\source\\repos\\Almutasim90\\Murasalat\\Recruitment\\wwwroot\\font-awesome\\css\\v5-font-face.css", "PackagePath": "staticwebassets\\font-awesome\\css\\v5-font-face.css"}, {"Id": "C:\\Users\\<USER>\\source\\repos\\Almutasim90\\Murasalat\\Recruitment\\wwwroot\\font-awesome\\css\\v5-font-face.min.css", "PackagePath": "staticwebassets\\font-awesome\\css\\v5-font-face.min.css"}, {"Id": "C:\\Users\\<USER>\\source\\repos\\Almutasim90\\Murasalat\\Recruitment\\wwwroot\\font-awesome\\js\\all.js", "PackagePath": "staticwebassets\\font-awesome\\js\\all.js"}, {"Id": "C:\\Users\\<USER>\\source\\repos\\Almutasim90\\Murasalat\\Recruitment\\wwwroot\\font-awesome\\js\\all.min.js", "PackagePath": "staticwebassets\\font-awesome\\js\\all.min.js"}, {"Id": "C:\\Users\\<USER>\\source\\repos\\Almutasim90\\Murasalat\\Recruitment\\wwwroot\\font-awesome\\js\\brands.js", "PackagePath": "staticwebassets\\font-awesome\\js\\brands.js"}, {"Id": "C:\\Users\\<USER>\\source\\repos\\Almutasim90\\Murasalat\\Recruitment\\wwwroot\\font-awesome\\js\\brands.min.js", "PackagePath": "staticwebassets\\font-awesome\\js\\brands.min.js"}, {"Id": "C:\\Users\\<USER>\\source\\repos\\Almutasim90\\Murasalat\\Recruitment\\wwwroot\\font-awesome\\js\\conflict-detection.js", "PackagePath": "staticwebassets\\font-awesome\\js\\conflict-detection.js"}, {"Id": "C:\\Users\\<USER>\\source\\repos\\Almutasim90\\Murasalat\\Recruitment\\wwwroot\\font-awesome\\js\\conflict-detection.min.js", "PackagePath": "staticwebassets\\font-awesome\\js\\conflict-detection.min.js"}, {"Id": "C:\\Users\\<USER>\\source\\repos\\Almutasim90\\Murasalat\\Recruitment\\wwwroot\\font-awesome\\js\\fontawesome.js", "PackagePath": "staticwebassets\\font-awesome\\js\\fontawesome.js"}, {"Id": "C:\\Users\\<USER>\\source\\repos\\Almutasim90\\Murasalat\\Recruitment\\wwwroot\\font-awesome\\js\\fontawesome.min.js", "PackagePath": "staticwebassets\\font-awesome\\js\\fontawesome.min.js"}, {"Id": "C:\\Users\\<USER>\\source\\repos\\Almutasim90\\Murasalat\\Recruitment\\wwwroot\\font-awesome\\js\\regular.js", "PackagePath": "staticwebassets\\font-awesome\\js\\regular.js"}, {"Id": "C:\\Users\\<USER>\\source\\repos\\Almutasim90\\Murasalat\\Recruitment\\wwwroot\\font-awesome\\js\\regular.min.js", "PackagePath": "staticwebassets\\font-awesome\\js\\regular.min.js"}, {"Id": "C:\\Users\\<USER>\\source\\repos\\Almutasim90\\Murasalat\\Recruitment\\wwwroot\\font-awesome\\js\\solid.js", "PackagePath": "staticwebassets\\font-awesome\\js\\solid.js"}, {"Id": "C:\\Users\\<USER>\\source\\repos\\Almutasim90\\Murasalat\\Recruitment\\wwwroot\\font-awesome\\js\\solid.min.js", "PackagePath": "staticwebassets\\font-awesome\\js\\solid.min.js"}, {"Id": "C:\\Users\\<USER>\\source\\repos\\Almutasim90\\Murasalat\\Recruitment\\wwwroot\\font-awesome\\js\\v4-shims.js", "PackagePath": "staticwebassets\\font-awesome\\js\\v4-shims.js"}, {"Id": "C:\\Users\\<USER>\\source\\repos\\Almutasim90\\Murasalat\\Recruitment\\wwwroot\\font-awesome\\js\\v4-shims.min.js", "PackagePath": "staticwebassets\\font-awesome\\js\\v4-shims.min.js"}, {"Id": "C:\\Users\\<USER>\\source\\repos\\Almutasim90\\Murasalat\\Recruitment\\wwwroot\\font-awesome\\sprites\\brands.svg", "PackagePath": "staticwebassets\\font-awesome\\sprites\\brands.svg"}, {"Id": "C:\\Users\\<USER>\\source\\repos\\Almutasim90\\Murasalat\\Recruitment\\wwwroot\\font-awesome\\sprites\\regular.svg", "PackagePath": "staticwebassets\\font-awesome\\sprites\\regular.svg"}, {"Id": "C:\\Users\\<USER>\\source\\repos\\Almutasim90\\Murasalat\\Recruitment\\wwwroot\\font-awesome\\sprites\\solid.svg", "PackagePath": "staticwebassets\\font-awesome\\sprites\\solid.svg"}, {"Id": "C:\\Users\\<USER>\\source\\repos\\Almutasim90\\Murasalat\\Recruitment\\wwwroot\\font-awesome\\webfonts\\fa-brands-400.ttf", "PackagePath": "staticwebassets\\font-awesome\\webfonts\\fa-brands-400.ttf"}, {"Id": "C:\\Users\\<USER>\\source\\repos\\Almutasim90\\Murasalat\\Recruitment\\wwwroot\\font-awesome\\webfonts\\fa-brands-400.woff2", "PackagePath": "staticwebassets\\font-awesome\\webfonts\\fa-brands-400.woff2"}, {"Id": "C:\\Users\\<USER>\\source\\repos\\Almutasim90\\Murasalat\\Recruitment\\wwwroot\\font-awesome\\webfonts\\fa-regular-400.ttf", "PackagePath": "staticwebassets\\font-awesome\\webfonts\\fa-regular-400.ttf"}, {"Id": "C:\\Users\\<USER>\\source\\repos\\Almutasim90\\Murasalat\\Recruitment\\wwwroot\\font-awesome\\webfonts\\fa-regular-400.woff2", "PackagePath": "staticwebassets\\font-awesome\\webfonts\\fa-regular-400.woff2"}, {"Id": "C:\\Users\\<USER>\\source\\repos\\Almutasim90\\Murasalat\\Recruitment\\wwwroot\\font-awesome\\webfonts\\fa-solid-900.ttf", "PackagePath": "staticwebassets\\font-awesome\\webfonts\\fa-solid-900.ttf"}, {"Id": "C:\\Users\\<USER>\\source\\repos\\Almutasim90\\Murasalat\\Recruitment\\wwwroot\\font-awesome\\webfonts\\fa-solid-900.woff2", "PackagePath": "staticwebassets\\font-awesome\\webfonts\\fa-solid-900.woff2"}, {"Id": "C:\\Users\\<USER>\\source\\repos\\Almutasim90\\Murasalat\\Recruitment\\wwwroot\\font-awesome\\webfonts\\fa-v4compatibility.ttf", "PackagePath": "staticwebassets\\font-awesome\\webfonts\\fa-v4compatibility.ttf"}, {"Id": "C:\\Users\\<USER>\\source\\repos\\Almutasim90\\Murasalat\\Recruitment\\wwwroot\\font-awesome\\webfonts\\fa-v4compatibility.woff2", "PackagePath": "staticwebassets\\font-awesome\\webfonts\\fa-v4compatibility.woff2"}, {"Id": "C:\\Users\\<USER>\\source\\repos\\Almutasim90\\Murasalat\\Recruitment\\wwwroot\\fonts\\Cairo-VariableFont_slnt,wght.ttf", "PackagePath": "staticwebassets\\fonts\\Cairo-VariableFont_slnt,wght.ttf"}, {"Id": "C:\\Users\\<USER>\\source\\repos\\Almutasim90\\Murasalat\\Recruitment\\wwwroot\\js\\site.js", "PackagePath": "staticwebassets\\js\\site.js"}, {"Id": "C:\\Users\\<USER>\\source\\repos\\Almutasim90\\Murasalat\\Recruitment\\wwwroot\\lib\\bootstrap\\LICENSE", "PackagePath": "staticwebassets\\lib\\bootstrap"}, {"Id": "C:\\Users\\<USER>\\source\\repos\\Almutasim90\\Murasalat\\Recruitment\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-grid.css", "PackagePath": "staticwebassets\\lib\\bootstrap\\dist\\css\\bootstrap-grid.css"}, {"Id": "C:\\Users\\<USER>\\source\\repos\\Almutasim90\\Murasalat\\Recruitment\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-grid.css.map", "PackagePath": "staticwebassets\\lib\\bootstrap\\dist\\css\\bootstrap-grid.css.map"}, {"Id": "C:\\Users\\<USER>\\source\\repos\\Almutasim90\\Murasalat\\Recruitment\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-grid.min.css", "PackagePath": "staticwebassets\\lib\\bootstrap\\dist\\css\\bootstrap-grid.min.css"}, {"Id": "C:\\Users\\<USER>\\source\\repos\\Almutasim90\\Murasalat\\Recruitment\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-grid.min.css.map", "PackagePath": "staticwebassets\\lib\\bootstrap\\dist\\css\\bootstrap-grid.min.css.map"}, {"Id": "C:\\Users\\<USER>\\source\\repos\\Almutasim90\\Murasalat\\Recruitment\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-grid.rtl.css", "PackagePath": "staticwebassets\\lib\\bootstrap\\dist\\css\\bootstrap-grid.rtl.css"}, {"Id": "C:\\Users\\<USER>\\source\\repos\\Almutasim90\\Murasalat\\Recruitment\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-grid.rtl.css.map", "PackagePath": "staticwebassets\\lib\\bootstrap\\dist\\css\\bootstrap-grid.rtl.css.map"}, {"Id": "C:\\Users\\<USER>\\source\\repos\\Almutasim90\\Murasalat\\Recruitment\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-grid.rtl.min.css", "PackagePath": "staticwebassets\\lib\\bootstrap\\dist\\css\\bootstrap-grid.rtl.min.css"}, {"Id": "C:\\Users\\<USER>\\source\\repos\\Almutasim90\\Murasalat\\Recruitment\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-grid.rtl.min.css.map", "PackagePath": "staticwebassets\\lib\\bootstrap\\dist\\css\\bootstrap-grid.rtl.min.css.map"}, {"Id": "C:\\Users\\<USER>\\source\\repos\\Almutasim90\\Murasalat\\Recruitment\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-reboot.css", "PackagePath": "staticwebassets\\lib\\bootstrap\\dist\\css\\bootstrap-reboot.css"}, {"Id": "C:\\Users\\<USER>\\source\\repos\\Almutasim90\\Murasalat\\Recruitment\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-reboot.css.map", "PackagePath": "staticwebassets\\lib\\bootstrap\\dist\\css\\bootstrap-reboot.css.map"}, {"Id": "C:\\Users\\<USER>\\source\\repos\\Almutasim90\\Murasalat\\Recruitment\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-reboot.min.css", "PackagePath": "staticwebassets\\lib\\bootstrap\\dist\\css\\bootstrap-reboot.min.css"}, {"Id": "C:\\Users\\<USER>\\source\\repos\\Almutasim90\\Murasalat\\Recruitment\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-reboot.min.css.map", "PackagePath": "staticwebassets\\lib\\bootstrap\\dist\\css\\bootstrap-reboot.min.css.map"}, {"Id": "C:\\Users\\<USER>\\source\\repos\\Almutasim90\\Murasalat\\Recruitment\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-reboot.rtl.css", "PackagePath": "staticwebassets\\lib\\bootstrap\\dist\\css\\bootstrap-reboot.rtl.css"}, {"Id": "C:\\Users\\<USER>\\source\\repos\\Almutasim90\\Murasalat\\Recruitment\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-reboot.rtl.css.map", "PackagePath": "staticwebassets\\lib\\bootstrap\\dist\\css\\bootstrap-reboot.rtl.css.map"}, {"Id": "C:\\Users\\<USER>\\source\\repos\\Almutasim90\\Murasalat\\Recruitment\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-reboot.rtl.min.css", "PackagePath": "staticwebassets\\lib\\bootstrap\\dist\\css\\bootstrap-reboot.rtl.min.css"}, {"Id": "C:\\Users\\<USER>\\source\\repos\\Almutasim90\\Murasalat\\Recruitment\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-reboot.rtl.min.css.map", "PackagePath": "staticwebassets\\lib\\bootstrap\\dist\\css\\bootstrap-reboot.rtl.min.css.map"}, {"Id": "C:\\Users\\<USER>\\source\\repos\\Almutasim90\\Murasalat\\Recruitment\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-utilities.css", "PackagePath": "staticwebassets\\lib\\bootstrap\\dist\\css\\bootstrap-utilities.css"}, {"Id": "C:\\Users\\<USER>\\source\\repos\\Almutasim90\\Murasalat\\Recruitment\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-utilities.css.map", "PackagePath": "staticwebassets\\lib\\bootstrap\\dist\\css\\bootstrap-utilities.css.map"}, {"Id": "C:\\Users\\<USER>\\source\\repos\\Almutasim90\\Murasalat\\Recruitment\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-utilities.min.css", "PackagePath": "staticwebassets\\lib\\bootstrap\\dist\\css\\bootstrap-utilities.min.css"}, {"Id": "C:\\Users\\<USER>\\source\\repos\\Almutasim90\\Murasalat\\Recruitment\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-utilities.min.css.map", "PackagePath": "staticwebassets\\lib\\bootstrap\\dist\\css\\bootstrap-utilities.min.css.map"}, {"Id": "C:\\Users\\<USER>\\source\\repos\\Almutasim90\\Murasalat\\Recruitment\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-utilities.rtl.css", "PackagePath": "staticwebassets\\lib\\bootstrap\\dist\\css\\bootstrap-utilities.rtl.css"}, {"Id": "C:\\Users\\<USER>\\source\\repos\\Almutasim90\\Murasalat\\Recruitment\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-utilities.rtl.css.map", "PackagePath": "staticwebassets\\lib\\bootstrap\\dist\\css\\bootstrap-utilities.rtl.css.map"}, {"Id": "C:\\Users\\<USER>\\source\\repos\\Almutasim90\\Murasalat\\Recruitment\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-utilities.rtl.min.css", "PackagePath": "staticwebassets\\lib\\bootstrap\\dist\\css\\bootstrap-utilities.rtl.min.css"}, {"Id": "C:\\Users\\<USER>\\source\\repos\\Almutasim90\\Murasalat\\Recruitment\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-utilities.rtl.min.css.map", "PackagePath": "staticwebassets\\lib\\bootstrap\\dist\\css\\bootstrap-utilities.rtl.min.css.map"}, {"Id": "C:\\Users\\<USER>\\source\\repos\\Almutasim90\\Murasalat\\Recruitment\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap.css", "PackagePath": "staticwebassets\\lib\\bootstrap\\dist\\css\\bootstrap.css"}, {"Id": "C:\\Users\\<USER>\\source\\repos\\Almutasim90\\Murasalat\\Recruitment\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap.css.map", "PackagePath": "staticwebassets\\lib\\bootstrap\\dist\\css\\bootstrap.css.map"}, {"Id": "C:\\Users\\<USER>\\source\\repos\\Almutasim90\\Murasalat\\Recruitment\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap.min.css", "PackagePath": "staticwebassets\\lib\\bootstrap\\dist\\css\\bootstrap.min.css"}, {"Id": "C:\\Users\\<USER>\\source\\repos\\Almutasim90\\Murasalat\\Recruitment\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap.min.css.map", "PackagePath": "staticwebassets\\lib\\bootstrap\\dist\\css\\bootstrap.min.css.map"}, {"Id": "C:\\Users\\<USER>\\source\\repos\\Almutasim90\\Murasalat\\Recruitment\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap.rtl.css", "PackagePath": "staticwebassets\\lib\\bootstrap\\dist\\css\\bootstrap.rtl.css"}, {"Id": "C:\\Users\\<USER>\\source\\repos\\Almutasim90\\Murasalat\\Recruitment\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap.rtl.css.map", "PackagePath": "staticwebassets\\lib\\bootstrap\\dist\\css\\bootstrap.rtl.css.map"}, {"Id": "C:\\Users\\<USER>\\source\\repos\\Almutasim90\\Murasalat\\Recruitment\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap.rtl.min.css", "PackagePath": "staticwebassets\\lib\\bootstrap\\dist\\css\\bootstrap.rtl.min.css"}, {"Id": "C:\\Users\\<USER>\\source\\repos\\Almutasim90\\Murasalat\\Recruitment\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap.rtl.min.css.map", "PackagePath": "staticwebassets\\lib\\bootstrap\\dist\\css\\bootstrap.rtl.min.css.map"}, {"Id": "C:\\Users\\<USER>\\source\\repos\\Almutasim90\\Murasalat\\Recruitment\\wwwroot\\lib\\bootstrap\\dist\\js\\bootstrap.bundle.js", "PackagePath": "staticwebassets\\lib\\bootstrap\\dist\\js\\bootstrap.bundle.js"}, {"Id": "C:\\Users\\<USER>\\source\\repos\\Almutasim90\\Murasalat\\Recruitment\\wwwroot\\lib\\bootstrap\\dist\\js\\bootstrap.bundle.js.map", "PackagePath": "staticwebassets\\lib\\bootstrap\\dist\\js\\bootstrap.bundle.js.map"}, {"Id": "C:\\Users\\<USER>\\source\\repos\\Almutasim90\\Murasalat\\Recruitment\\wwwroot\\lib\\bootstrap\\dist\\js\\bootstrap.bundle.min.js", "PackagePath": "staticwebassets\\lib\\bootstrap\\dist\\js\\bootstrap.bundle.min.js"}, {"Id": "C:\\Users\\<USER>\\source\\repos\\Almutasim90\\Murasalat\\Recruitment\\wwwroot\\lib\\bootstrap\\dist\\js\\bootstrap.bundle.min.js.map", "PackagePath": "staticwebassets\\lib\\bootstrap\\dist\\js\\bootstrap.bundle.min.js.map"}, {"Id": "C:\\Users\\<USER>\\source\\repos\\Almutasim90\\Murasalat\\Recruitment\\wwwroot\\lib\\bootstrap\\dist\\js\\bootstrap.esm.js", "PackagePath": "staticwebassets\\lib\\bootstrap\\dist\\js\\bootstrap.esm.js"}, {"Id": "C:\\Users\\<USER>\\source\\repos\\Almutasim90\\Murasalat\\Recruitment\\wwwroot\\lib\\bootstrap\\dist\\js\\bootstrap.esm.js.map", "PackagePath": "staticwebassets\\lib\\bootstrap\\dist\\js\\bootstrap.esm.js.map"}, {"Id": "C:\\Users\\<USER>\\source\\repos\\Almutasim90\\Murasalat\\Recruitment\\wwwroot\\lib\\bootstrap\\dist\\js\\bootstrap.esm.min.js", "PackagePath": "staticwebassets\\lib\\bootstrap\\dist\\js\\bootstrap.esm.min.js"}, {"Id": "C:\\Users\\<USER>\\source\\repos\\Almutasim90\\Murasalat\\Recruitment\\wwwroot\\lib\\bootstrap\\dist\\js\\bootstrap.esm.min.js.map", "PackagePath": "staticwebassets\\lib\\bootstrap\\dist\\js\\bootstrap.esm.min.js.map"}, {"Id": "C:\\Users\\<USER>\\source\\repos\\Almutasim90\\Murasalat\\Recruitment\\wwwroot\\lib\\bootstrap\\dist\\js\\bootstrap.js", "PackagePath": "staticwebassets\\lib\\bootstrap\\dist\\js\\bootstrap.js"}, {"Id": "C:\\Users\\<USER>\\source\\repos\\Almutasim90\\Murasalat\\Recruitment\\wwwroot\\lib\\bootstrap\\dist\\js\\bootstrap.js.map", "PackagePath": "staticwebassets\\lib\\bootstrap\\dist\\js\\bootstrap.js.map"}, {"Id": "C:\\Users\\<USER>\\source\\repos\\Almutasim90\\Murasalat\\Recruitment\\wwwroot\\lib\\bootstrap\\dist\\js\\bootstrap.min.js", "PackagePath": "staticwebassets\\lib\\bootstrap\\dist\\js\\bootstrap.min.js"}, {"Id": "C:\\Users\\<USER>\\source\\repos\\Almutasim90\\Murasalat\\Recruitment\\wwwroot\\lib\\bootstrap\\dist\\js\\bootstrap.min.js.map", "PackagePath": "staticwebassets\\lib\\bootstrap\\dist\\js\\bootstrap.min.js.map"}, {"Id": "C:\\Users\\<USER>\\source\\repos\\Almutasim90\\Murasalat\\Recruitment\\wwwroot\\lib\\jquery-validation-unobtrusive\\LICENSE.txt", "PackagePath": "staticwebassets\\lib\\jquery-validation-unobtrusive\\LICENSE.txt"}, {"Id": "C:\\Users\\<USER>\\source\\repos\\Almutasim90\\Murasalat\\Recruitment\\wwwroot\\lib\\jquery-validation-unobtrusive\\dist\\jquery.validate.unobtrusive.js", "PackagePath": "staticwebassets\\lib\\jquery-validation-unobtrusive\\dist\\jquery.validate.unobtrusive.js"}, {"Id": "C:\\Users\\<USER>\\source\\repos\\Almutasim90\\Murasalat\\Recruitment\\wwwroot\\lib\\jquery-validation-unobtrusive\\dist\\jquery.validate.unobtrusive.min.js", "PackagePath": "staticwebassets\\lib\\jquery-validation-unobtrusive\\dist\\jquery.validate.unobtrusive.min.js"}, {"Id": "C:\\Users\\<USER>\\source\\repos\\Almutasim90\\Murasalat\\Recruitment\\wwwroot\\lib\\jquery-validation\\LICENSE.md", "PackagePath": "staticwebassets\\lib\\jquery-validation\\LICENSE.md"}, {"Id": "C:\\Users\\<USER>\\source\\repos\\Almutasim90\\Murasalat\\Recruitment\\wwwroot\\lib\\jquery-validation\\dist\\additional-methods.js", "PackagePath": "staticwebassets\\lib\\jquery-validation\\dist\\additional-methods.js"}, {"Id": "C:\\Users\\<USER>\\source\\repos\\Almutasim90\\Murasalat\\Recruitment\\wwwroot\\lib\\jquery-validation\\dist\\additional-methods.min.js", "PackagePath": "staticwebassets\\lib\\jquery-validation\\dist\\additional-methods.min.js"}, {"Id": "C:\\Users\\<USER>\\source\\repos\\Almutasim90\\Murasalat\\Recruitment\\wwwroot\\lib\\jquery-validation\\dist\\jquery.validate.js", "PackagePath": "staticwebassets\\lib\\jquery-validation\\dist\\jquery.validate.js"}, {"Id": "C:\\Users\\<USER>\\source\\repos\\Almutasim90\\Murasalat\\Recruitment\\wwwroot\\lib\\jquery-validation\\dist\\jquery.validate.min.js", "PackagePath": "staticwebassets\\lib\\jquery-validation\\dist\\jquery.validate.min.js"}, {"Id": "C:\\Users\\<USER>\\source\\repos\\Almutasim90\\Murasalat\\Recruitment\\wwwroot\\lib\\jquery\\LICENSE.txt", "PackagePath": "staticwebassets\\lib\\jquery\\LICENSE.txt"}, {"Id": "C:\\Users\\<USER>\\source\\repos\\Almutasim90\\Murasalat\\Recruitment\\wwwroot\\lib\\jquery\\dist\\jquery.js", "PackagePath": "staticwebassets\\lib\\jquery\\dist\\jquery.js"}, {"Id": "C:\\Users\\<USER>\\source\\repos\\Almutasim90\\Murasalat\\Recruitment\\wwwroot\\lib\\jquery\\dist\\jquery.min.js", "PackagePath": "staticwebassets\\lib\\jquery\\dist\\jquery.min.js"}, {"Id": "C:\\Users\\<USER>\\source\\repos\\Almutasim90\\Murasalat\\Recruitment\\wwwroot\\lib\\jquery\\dist\\jquery.min.map", "PackagePath": "staticwebassets\\lib\\jquery\\dist\\jquery.min.map"}, {"Id": "C:\\Users\\<USER>\\source\\repos\\Almutasim90\\Murasalat\\Recruitment\\wwwroot\\lib\\jquery\\dist\\jquery.slim.js", "PackagePath": "staticwebassets\\lib\\jquery\\dist\\jquery.slim.js"}, {"Id": "C:\\Users\\<USER>\\source\\repos\\Almutasim90\\Murasalat\\Recruitment\\wwwroot\\lib\\jquery\\dist\\jquery.slim.min.js", "PackagePath": "staticwebassets\\lib\\jquery\\dist\\jquery.slim.min.js"}, {"Id": "C:\\Users\\<USER>\\source\\repos\\Almutasim90\\Murasalat\\Recruitment\\wwwroot\\lib\\jquery\\dist\\jquery.slim.min.map", "PackagePath": "staticwebassets\\lib\\jquery\\dist\\jquery.slim.min.map"}, {"Id": "obj\\Debug\\net9.0\\staticwebassets\\msbuild.Murasalat.Microsoft.AspNetCore.StaticWebAssetEndpoints.props", "PackagePath": "build\\Microsoft.AspNetCore.StaticWebAssetEndpoints.props"}, {"Id": "obj\\Debug\\net9.0\\staticwebassets\\msbuild.Murasalat.Microsoft.AspNetCore.StaticWebAssets.props", "PackagePath": "build\\Microsoft.AspNetCore.StaticWebAssets.props"}, {"Id": "obj\\Debug\\net9.0\\staticwebassets\\msbuild.build.Murasalat.props", "PackagePath": "build\\Murasalat.props"}, {"Id": "obj\\Debug\\net9.0\\staticwebassets\\msbuild.buildMultiTargeting.Murasalat.props", "PackagePath": "buildMultiTargeting\\Murasalat.props"}, {"Id": "obj\\Debug\\net9.0\\staticwebassets\\msbuild.buildTransitive.Murasalat.props", "PackagePath": "buildTransitive\\Murasalat.props"}], "ElementsToRemove": []}