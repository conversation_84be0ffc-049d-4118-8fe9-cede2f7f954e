﻿@model IEnumerable<OmanInternship.Models.Location>

@{
    ViewData["Title"] = "Index";
    Layout = "~/Views/Shared/_Layout.cshtml";
}



<p>
    <a asp-action="Create">Create New</a>
</p>

<ejs-grid id="Grid" dataSource="@Model" allowGrouping="true" allowSorting="true"
          allowReordering="true" allowPaging="true"
          gridLines="None" toolbar="@(new List<string>() { "Search" })">
    <e-grid-pagesettings pageSize="7"></e-grid-pagesettings>



    <e-grid-columns>


        <e-grid-column headerText="Location" field="LocationName"></e-grid-column>
        <e-grid-column headerText="Actions" template="#actionColumntemplate"></e-grid-column>
    </e-grid-columns>
</ejs-grid>
@* this script for action button*@


<script id="actionColumntemplate" type="text/x-template">
    <a class="btn btn-primary btn-sm mx-1 " href="/Location/Details/${id}">Details</a>
    <a class="btn btn-warning btn-sm mx-1 " href="/Location/Edit/${id}">Edit</a>
    <a class="btn btn-danger btn-sm mx-1" href="/Location/Delete/${id}">Delete</a>
</script>
