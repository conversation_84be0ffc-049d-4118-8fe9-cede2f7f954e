﻿<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="utf-8" />
    <meta name="viewport" content="width=device-width, initial-scale=1.0" />
    <title>@ViewData["Title"] - نظام إدارة التجنيد</title>

    <link rel="stylesheet" href="~/lib/bootstrap/dist/css/bootstrap.rtl.min.css" />
  
    <link href="~/font-awesome/css/all.css" rel="stylesheet" />
    <link href="~/font-awesome/css/all.min.css" rel="stylesheet" />
    <link rel="stylesheet" href="//cdn.datatables.net/1.11.5/css/dataTables.bootstrap5.min.css" />
    <link rel="stylesheet" href="~/css/sidebar.css" asp-append-version="true" />
    <link rel="stylesheet" href="~/css/site.css" asp-append-version="true" />
    <style>
        /* Additional styles for scrollable submenus */
        .submenu {
            max-height: 300px; /* Adjust based on your needs */
            overflow-y: auto;
            scrollbar-width: thin;
            scrollbar-color: #4e73df #f8f9fa;
        }

            /* Custom scrollbar for Webkit browsers */
            .submenu::-webkit-scrollbar {
                width: 6px;
            }

            .submenu::-webkit-scrollbar-track {
                background: #f8f9fa;
                border-radius: 3px;
            }

            .submenu::-webkit-scrollbar-thumb {
                background-color: #4e73df;
                border-radius: 3px;
            }

            /* Improved submenu item styling */
            .submenu .nav-link {
                padding-right: 2.5rem !important;
                padding-left: 1rem !important;
                white-space: nowrap;
                overflow: hidden;
                text-overflow: ellipsis;
            }

        /* Better spacing for collapsed state */
        .sidebar.collapsed .submenu {
            display: none !important;
        }

        /* Smooth transitions */
        .submenu {
            transition: max-height 0.3s ease;
        }

            /* Active item highlighting */
            .submenu .nav-link.active {
                background-color: rgba(78, 115, 223, 0.1);
                border-right: 3px solid #4e73df;
            }
    </style>
</head>
<body>
    <div class="sidebar">
        <div class="brand-container">
            <h4 class="menu-text mb-0 fw-bolder">نظام إدارة التجنيد</h4>
            <button class="toggle-btn">
                <i class="fas fa-bars"></i>
            </button>
        </div>

        <ul class="nav flex-column">
            <li class="nav-item">
                <a class="nav-link" asp-controller="Home" asp-action="Index">
                    <i class="fas fa-home"></i>
                    <span class="menu-text">الرئيسية</span>
                </a>
            </li>

            @* Authentication section commented out as in original *@

            <li class="nav-item has-submenu">
                <a class="nav-link" data-bs-toggle="collapse" href="#controlPanel" role="button" aria-expanded="false">
                    <i class="fas fa-cogs"></i>
                    <span class="menu-text">لوحة التحكم</span>
                    <i class="fas fa-chevron-down mr-auto submenu-icon"></i>
                </a>

                <div class="collapse submenu" id="controlPanel">
                    <div class="submenu-inner">
                        <a class="nav-link" asp-controller="" asp-action="">
                            <i class="fas fa-users"></i>
                            <span class="menu-text">فئات المرشحين</span>
                        </a>
                        <a class="nav-link" asp-controller="" asp-action="">
                            <i class="fas fa-users"></i>
                            <span class="menu-text">أسماء المرشحين</span>
                        </a>
                        <a class="nav-link" href="#">
                            <i class="fas fa-clipboard-list"></i>
                            <span class="menu-text">نماذج التقييم</span>
                        </a>
                        <a class="nav-link" href="#">
                            <i class="fas fa-user-tie"></i>
                            <span class="menu-text">أعضاء اللجنة</span>
                        </a>
                        <a class="nav-link" href="#">
                            <i class="fas fa-key"></i>
                            <span class="menu-text">الصلاحيات</span>
                        </a>
                        <a class="nav-link" asp-controller="" asp-action="">
                            <i class="fas fa-users-cog"></i>
                            <span class="menu-text">إدارة المستخدمين</span>
                        </a>
                        <!-- Additional items to demonstrate scrolling -->
                        <a class="nav-link" href="#">
                            <i class="fas fa-calendar-alt"></i>
                            <span class="menu-text">جدول المقابلات</span>
                        </a>
                        <a class="nav-link" href="#">
                            <i class="fas fa-file-pdf"></i>
                            <span class="menu-text">التقارير</span>
                        </a>
                        <a class="nav-link" href="#">
                            <i class="fas fa-chart-bar"></i>
                            <span class="menu-text">الإحصائيات</span>
                        </a>
                        <a class="nav-link" href="#">
                            <i class="fas fa-envelope"></i>
                            <span class="menu-text">المراسلات</span>
                        </a>
                        <a class="nav-link" href="#">
                            <i class="fas fa-cog"></i>
                            <span class="menu-text">الإعدادات</span>
                        </a>
                    </div>
                </div>
            </li>

            <!-- Additional menu items can be added here following the same pattern -->

        </ul>
    </div>

    <div class="main-content">
        <main role="main" class="pb-3">
            @RenderBody()
        </main>
    </div>

    <script src="~/lib/jquery/dist/jquery.min.js"></script>
    <script src="~/lib/bootstrap/dist/js/bootstrap.bundle.min.js"></script>
    <script src="~/font-awesome/js/all.js"></script>
    <script src="~/font-awesome/js/all.min.js"></script>
    
    <script src="~/datatables.net-bs5/datatables.bootstrap5.js"></script>
    <script src="~/datatables.net-bs5/datatables.bootstrap5.min.js"></script>
    <script src="~/lib/jquery-validation/dist/jquery.validate.min.js"></script>
    <script src="~/js/site.js" asp-append-version="true"></script>

    <script>
        document.querySelector('.toggle-btn').addEventListener('click', function () {
            document.querySelector('.sidebar').classList.toggle('collapsed');
            document.querySelector('.main-content').classList.toggle('expanded');
        });

        // Enhance submenu behavior
        document.querySelectorAll('.has-submenu > a').forEach(item => {
            item.addEventListener('click', function (e) {
                const icon = this.querySelector('.submenu-icon');
                if (icon) {
                    icon.classList.toggle('fa-chevron-down');
                    icon.classList.toggle('fa-chevron-up');
                }
            });
        });
    </script>
    @await RenderSectionAsync("Scripts", required: false)
</body>
</html>