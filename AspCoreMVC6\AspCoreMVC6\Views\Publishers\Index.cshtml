﻿@model IEnumerable<AspCoreMVC6.Models.Publisher>

@{
    ViewData["Title"] = "Index";
}

<h1>Index</h1>

<p>
    <a asp-action="Create">Create New</a>
</p>
<table class="table">
    <thead>
        <tr>
            <th>
                @Html.DisplayNameFor(model => model.PublisherName)
            </th>
            <th></th>
        </tr>
    </thead>
    <tbody>
@foreach (var item in Model) {
        <tr>
            <td>
                @Html.DisplayFor(modelItem => item.PublisherName)
            </td>
            <td>
                <a asp-action="Edit" asp-route-id="@item.PublisherId">Edit</a> |
                <a asp-action="Details" asp-route-id="@item.PublisherId">Details</a> |
                <a asp-action="Delete" asp-route-id="@item.PublisherId">Delete</a>
            </td>
        </tr>
}
    </tbody>
</table>
