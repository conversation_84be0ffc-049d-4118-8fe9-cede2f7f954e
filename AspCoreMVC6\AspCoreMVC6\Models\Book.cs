﻿using System.ComponentModel.DataAnnotations;
using System.ComponentModel.DataAnnotations.Schema;

namespace AspCoreMVC6.Models
{
    public class Book
    {
        [Key]
        [DatabaseGenerated(DatabaseGeneratedOption.Identity)]
        public int Id { get; set; }

        [Required(ErrorMessage ="{0} is required")]
        //[Display(Name = " عنوان الكتاب")]
        public string Title { get; set; }
        [ForeignKey("PublisherId")]
        //[Display(Name = "دار النشر")]
        public int PublisherId { get; set; }
        [ForeignKey("AuthorId")]
        //[Display(Name = "المؤلف")]
        public int AuthorId { get; set; }
        [ForeignKey("CategoryId")]
        //[Display(Name = "الصنف")]
        public int CategoryId { get; set; }

        
        public virtual Author Author { get; set; }  
        public  virtual Publisher Publisher { get; set; }
        public virtual  Category Category { get; set; }

      

    }
}
