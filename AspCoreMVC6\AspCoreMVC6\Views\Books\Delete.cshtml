﻿@model AspCoreMVC6.Models.Book

@{
    ViewData["Title"] = "Delete";
}

<h1>Delete</h1>

<h3>Are you sure you want to delete this?</h3>
<div>
    <h4>Book</h4>
    <hr />
    <dl class="row">
        <dt class = "col-sm-2">
            @Html.DisplayNameFor(model => model.Title)
        </dt>
        <dd class = "col-sm-10">
            @Html.DisplayFor(model => model.Title)
        </dd>
        <dt class = "col-sm-2">
            @Html.DisplayNameFor(model => model.Author)
        </dt>
        <dd class = "col-sm-10">
            @Html.DisplayFor(model => model.Author.AuthorName)
        </dd>
        <dt class = "col-sm-2">
            @Html.DisplayNameFor(model => model.Publisher)
        </dt>
        <dd class = "col-sm-10">
            @Html.DisplayFor(model => model.Publisher.PublisherName)
        </dd>
        <dt class = "col-sm-2">
            @Html.DisplayNameFor(model => model.Category)
        </dt>
        <dd class = "col-sm-10">
            @Html.DisplayFor(model => model.Category.Categoryname)
        </dd>
    </dl>
    
    <form asp-action="Delete">
        <input type="hidden" asp-for="Id" />
        <input type="submit" value="Delete" class="btn btn-danger" /> |
        <a asp-action="Index">Back to List</a>
    </form>
</div>
